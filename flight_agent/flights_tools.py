import asyncio
import csv
import json
import re
import uuid
from collections import defaultdict
from datetime import datetime
from functools import partial
from io import String<PERSON>
from typing import Any, Callable, Coroutine, Dict, List, cast
from zoneinfo import ZoneInfo

import airportsdata
import structlog
from fastapi import HTTPException
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from baml_client.types import SeatSelectionForFlight
from front_of_house_agent import flight_utils
from front_of_house_agent.common_models import FlightSearchParams, FlightSearchType
from server.database.models.user import User as UserDB
from server.schemas.authenticate.user import User
from server.schemas.user_profile.payment_information import PaymentInformationRequest
from server.schemas.user_profile.personal_information import PersonalInformationExtendedRequest
from server.services.trips.flight_credits_api import flight_credits_api
from server.services.user_profile.loyalty_programs import get_user_profile_flights_loyalty_programs
from server.services.user_profile.payment_information import (
    get_user_profile_payment_information,
    is_user_profile_payment_information_complete,
    save_payment_information_spotnana,
)
from server.services.user_profile.personal_information import (
    get_user_profile_personal_information,
    is_user_profile_personal_information_complete,
    save_personal_information_spotnana,
)
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.spotnana_api import SpotnanaHelper, spotnana_api
from virtual_travel_agent.common_models import AgentErrorCode
from virtual_travel_agent.helpers import atime_function, console_masks

flight_log_mask = console_masks["flight"]

# airline's allies, airlines in the same alliance. e.g. {"A3": ["AC", "A3"]}
airline_allies_mapping: dict[str, List[str]] = {}
# airline's alliance id, used for spotnana alliance search e.g. {"A3": "STAR_ALLIANCE"}
airline_alliance_id_mapping: dict[str, str] = {}
# alliance airlines list. e.g. {"Star Alliance": ["A3", "AC"]}
alliance_airlines_mapping: dict[str, List[str]] = defaultdict(list)
airline_loyalty_program_mapping: dict[str, str] = {}

airline_name_to_iata_code: dict[str, str] = {}
airline_iata_code_to_name: dict[str, str] = {}
airports = airportsdata.load("IATA")

with open("data/airline_alliance_map.json", "r") as file:
    # {"VANILLA_ALLIANCE": "French", "U_FLY_ALLIANCE": "Hong Kong, mainland China, and Southeast Asia", "VALUE_ALLIANCE": "Asia-Pacific"}
    alliance_id_map = {"Star Alliance": "STAR_ALLIANCE", "Oneworld": "ONEWORLD", "SkyTeam": "SKYTEAM"}
    aa_map = json.load(file)
    for airline_code in aa_map:
        airline = aa_map[airline_code]
        airline_name_to_iata_code[airline["airline_name"]] = airline_code
        airline_iata_code_to_name[airline_code] = airline["airline_name"]
        airline_allies_mapping[airline_code] = airline["allies"]
        airline_loyalty_program_mapping[airline_code] = airline["frequent_flyer_program"]

        if airline["alliance_name"]:
            alliance_airlines_mapping[airline["alliance_name"]].append(airline_code)
            airline_alliance_id_mapping[airline_code] = alliance_id_map.get(airline["alliance_name"], "")


class SpotnanaRetryableException(Exception):
    pass


class FlightSearchTools:
    token_url = f"{settings.SPOTNANA_HOST}/get-auth-token"
    user_guid = settings.SPOTNANA_USER_GUID
    vgs_url = f"{settings.SPOTNANA_VGS_HOST}/v1/credit-card-create"
    spotnana_confirm_url = f"{settings.SPOTNANA_HOST}/v1/confirm-card"
    airline_dict = {}

    headers = {
        "Content-Type": "application/json",
        "x-spotnana-api-key": settings.SPOTNANA_API_KEY,
    }
    data = {
        "grant_type": "client_credentials",
        "clientId": settings.SPOTNANA_CLIENT_ID,
        "clientSecret": settings.SPOTNANA_CLIENT_SECRET,
    }

    @staticmethod
    async def acall_spotnana_api(
        url,
        data: Any = None,
        params: Any = None,
        request_filename: str | None = None,
        response_filename: str | None = None,
    ) -> Any:
        if settings.RUN_CONTEXT != "server":
            if request_filename:
                with open(request_filename, "w") as file:
                    if data:
                        file.write(json.dumps(data, indent=4))
                    elif params:
                        file.write(json.dumps(params, indent=4))

        if data:
            response = await spotnana_api.post(url, headers=FlightSearchTools.headers, data=data)
        else:
            response = await spotnana_api.get(url, headers=FlightSearchTools.headers, params=params)

        if settings.RUN_CONTEXT != "server":
            if response_filename:
                with open(response_filename, "w") as file:
                    file.write(json.dumps(response, indent=4))

        trip_id = structlog.contextvars.get_contextvars().get("trip_id", "unknown")
        user_id = structlog.contextvars.get_contextvars().get("user_id", "unknown")
        search_result = re.search(r"/v[12]/(.+)", url) if url else None
        method = search_result.group(1).upper() if search_result else ""
        flight_utils.log_request_to_firehose(
            source="SPOTNANA",
            user_id=user_id,
            trip_id=trip_id,
            params=data or params,
            response=response,
            method=method,
        )
        errorMessages = response.get("errorMessages")
        if errorMessages and len(errorMessages) > 0:
            errorStr = f"Spotnana Error: {errorMessages[0].get('errorCode')} - {errorMessages[0].get('errorDetail')}\n\nSpotnana debugIdentifier: {response.get('debugIdentifier', '')}"  # type: ignore
            raise Exception(errorStr)
        return response

    @staticmethod
    def flatten_flight_attributes(flight: dict[str, Any]) -> dict:
        amenity_keys = {}
        for amenity in flight.get("amenities", []):
            for key, value in amenity.items():
                if "Amenity" in key:
                    amenity_keys[key] = value.get("displayText", "")

        flight_attributes = []
        for attribute in flight.get("flightAttributes", []):
            flight_attributes.append(attribute.get("description", ""))

        return {**amenity_keys, "flight_attributes": flight_attributes}

    @staticmethod
    def map_flights_in_itin(itinerary) -> List[dict[str, Any]]:
        flights = []

        for leg in itinerary["legs"]:
            # Iterate through each flight in the leg
            for _, flight in enumerate(leg["flights"], start=1):
                # Extract amenities from the 'amenities' section
                flight_id = flight.get("id")

                flights.append(
                    {
                        "flight_id": flight_id,
                        "origin": flight.get("flightData", {}).get("flightData", {}).get("origin"),
                        "destination": flight.get("flightData", {}).get("flightData", {}).get("destination"),
                        "flight_type": flight.get("flightData", {})
                        .get("flightData", {})
                        .get("equipment", {})
                        .get("type"),
                        "airline_code": flight.get("flightData", {})
                        .get("flightData", {})
                        .get("marketing", {})
                        .get("airlineCode"),
                        "airline_name": FlightSearchTools.airline_dict.get(
                            flight.get("flightData", {}).get("flightData", {}).get("marketing", {}).get("airlineCode"),
                            "",
                        ),
                        "flight_number": flight.get("flightData", {})
                        .get("flightData", {})
                        .get("marketing", {})
                        .get("num"),
                    }
                )

        return flights

    @staticmethod
    def is_us_domestic_flight(
        departure_code: str | None,
        arrival_code: str | None,
        is_departure_city_code: bool | None = None,
        is_arrival_city_code: bool | None = None,
    ) -> bool:
        if not departure_code or not arrival_code:
            return False

        if is_departure_city_code:
            departure_is_us = departure_code in settings.US_METRO_AREA_CODES
        else:
            departure_is_us = airports.get(departure_code, {}).get("country") == "US"

        if not departure_is_us:
            return False

        if is_arrival_city_code:
            arrival_is_us = arrival_code in settings.US_METRO_AREA_CODES
        else:
            arrival_is_us = airports.get(arrival_code, {}).get("country") == "US"

        return arrival_is_us

    @staticmethod
    def flight_to_readable_string(flight: dict[str, Any]) -> str:
        readable_string = (
            f"{flight['airline_code']}{flight['flight_number']}: {flight['origin']} to {flight['destination']}"
        )

        return readable_string

    @staticmethod
    def flight_to_readable_string2(flight: dict[str, Any]) -> str:
        readable_string = f"{flight['airline_code']}{flight['flight_number']}: {flight.get('origin', {}).get('airportCode')} to {flight.get('destination', {}).get('airportCode')}"

        return readable_string

    @staticmethod
    def convert_seat_map_to_csv(seat_map: Dict[str, Any]) -> tuple[str, dict[str, float]]:
        """
        Convert seat map to CSV format and return both the CSV string and a list of available seats.

        Returns:
            tuple: (csv_content, available_seats_list)
                - csv_content: CSV string representation of the seat map
                - available_seats_list: List of available seats with format [seat_id]
                  where seat_id is the concatenated row and column (e.g., "12A")
        """
        output = StringIO()
        csv_writer = csv.writer(output)

        # Write the header row
        csv_writer.writerow(["ROW", "COLUMN", "SEAT_TYPE", "SEAT_LOCATION", "IS_WING", "PRICE"])
        seat_col_metadata = {}
        wing_row_start = seat_map.get("wingRows", {}).get("min")
        wing_row_end = seat_map.get("wingRows", {}).get("max")
        output_rows = {}
        for cabin in seat_map.get("cabinSections", []):
            for column in cabin.get("columnSections", []):
                for col in column.get("columns", []):
                    seat_col_metadata[col.get("columnNumber")] = {"position": col.get("position")}

            for row in cabin.get("rowSections", []):
                seat_sections = row.get("seatSections", [])
                current_locations = list(cabin.get("locations", []))
                selectable_for_current_rows = {}
                for seat_section in seat_sections:
                    current_locations.extend(seat_section.get("location", []))
                    if (
                        seat_section.get("limitations") is not None
                        and len(
                            [
                                a
                                for a in seat_section.get("limitations")
                                if a is not None
                                and a
                                not in [
                                    "NOT_ALLOWED_FOR_INFANT",
                                    "RESTRICTED_RECLINE",
                                    "NOT_SUITABLE_FOR_CHILD",
                                    "WINDOW_SEAT_WITHOUT_WINDOW",
                                    "RESTRICTED_GENERAL",
                                    "NOT_ALLOWED_FOR_UNACCOMPANIED_MINOR",
                                ]
                            ]
                        )
                        > 0
                    ):
                        continue
                    for col in seat_section.get("columnNumbers", []):
                        selectable_for_current_rows[col] = True

                min_row = row.get("rowNumbers", {}).get("min")
                max_row = row.get("rowNumbers", {}).get("max")
                available_seats = row.get("availableSeats", [])
                for i in range(0, max_row - min_row + 1):
                    row_num = min_row + i
                    for col in available_seats[i].get("columnNumber", []):
                        if col not in selectable_for_current_rows:
                            continue
                        seat_type = seat_col_metadata[col].get("position")
                        seat_location = current_locations
                        is_wing_row = wing_row_start <= row_num <= wing_row_end
                        seat_id = f"{row_num}{col}"

                        seat_price = 0
                        for seat_section in seat_sections:
                            if col in seat_section.get("columnNumbers", []):
                                price_info = seat_section.get("price", {})
                                if price_info and price_info.get("totalAmount", {}).get("amount"):
                                    seat_price = price_info.get("totalAmount", {}).get("amount", 0)
                                break
                        output_rows[seat_id] = seat_price
                        csv_writer.writerow([row_num, col, seat_type, seat_location, is_wing_row, seat_price])

        csv_content = output.getvalue()
        output.close()
        if len(output_rows) == 0:
            return "", {}
        return csv_content, output_rows

    @staticmethod
    def flattenFlights_itin(
        itin: dict,
        flightData_dict: dict[str, Any] | None = None,
        total_seat_price: float | None = None,
    ) -> List[dict[str, Any]]:
        legs = cast(List[dict[str, Any]], itin.get("legs"))
        flight_options = []
        for leg in legs:
            totalFare = itin.get("fareInfo").get("totalFare")  # type: ignore

            # for now we are ignoring flight stop info (e.g. layovers), we are
            # just looking at the first and last flight in the leg
            segments = cast(List[dict[str, Any]], leg.get("flights"))
            if flightData_dict:
                # need to merge the flightData_dict info with the current flight info, not just rely on the flightData_dict
                # the flightData_dict has the specific flight info but not
                # amenties or attributes like the flight has
                for segment in segments:
                    segment["flightData"]["flightData"] = flightData_dict.get(
                        segment["flightData"]["flightRef"]["flightRef"], {}
                    )
            firstSegment = segments[0]
            lastSegment = segments[-1]

            fareRules = leg.get("travelerInfos")[0].get("fareRules")  # type: ignore
            itin_id = itin.get("itineraryId")

            firstSegmentData = firstSegment["flightData"]["flightData"]
            lastSegmentData = lastSegment["flightData"]["flightData"]

            # calculate the duration of the flight including all segments
            flight_departure_dt = datetime.fromisoformat(firstSegmentData["departureDateTime"]["iso8601"]).replace(
                tzinfo=ZoneInfo(
                    flight_utils.get_timezone_from_iata_airport_code(firstSegmentData["origin"]["airportCode"])  # type: ignore
                )
            )
            flight_arrival_dt = datetime.fromisoformat(lastSegmentData["arrivalDateTime"]["iso8601"]).replace(
                tzinfo=ZoneInfo(
                    flight_utils.get_timezone_from_iata_airport_code(lastSegmentData["destination"]["airportCode"])  # type: ignore
                )
            )
            flight_duration = flight_arrival_dt - flight_departure_dt
            hours, remainder = divmod(flight_duration.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            flight_duration_iso = f"PT{hours}H{minutes}M"
            flight_duration_in_seconds = flight_duration.total_seconds()

            departureGate = ""
            if "departureGate" in firstSegmentData:
                terminal = firstSegmentData.get("departureGate").get("terminal", "")
                terminal = f"Terminal {terminal}" if terminal else ""
                gate = firstSegmentData.get("departureGate").get("gate", "")
                gate = f"Gate {gate}" if gate else ""
                departureGate = " ".join([terminal, gate])
            arrivalGate = ""
            if "arrivalGate" in lastSegmentData:
                terminal = lastSegmentData.get("arrivalGate").get("terminal", "")
                terminal = f"Terminal {terminal}" if terminal else ""
                gate = lastSegmentData.get("arrivalGate").get("gate", "")
                gate = f"Gate {gate}" if gate else ""
                arrivalGate = " ".join([terminal, gate])

            total_distance_km = sum(
                [segment.get("carbonEmission", {}).get("flightDistanceKm", 0) for segment in segments]
            )
            total_distance_miles = round(total_distance_km * 0.621371, 2)  # Convert km to miles

            # Flight metadata, but flatterned
            flight_option = {
                "id_token_key": itin_id,
                "origin": firstSegmentData["origin"]["airportCode"],
                "origin_name": firstSegmentData["origin"]["airportName"],
                "destination": lastSegmentData["destination"]["airportCode"],
                "destination_name": lastSegmentData["destination"]["airportName"],
                "departure_time": firstSegmentData["departureDateTime"]["iso8601"],
                "departure_timezone": flight_utils.get_timezone_from_iata_airport_code(
                    firstSegmentData["origin"]["airportCode"]
                ),
                "departure_gate": departureGate,
                "arrival_time": lastSegmentData["arrivalDateTime"]["iso8601"],
                "arrival_timezone": flight_utils.get_timezone_from_iata_airport_code(
                    lastSegmentData["destination"]["airportCode"]
                ),
                "arrival_gate": arrivalGate,
                # the following are for the first flight in the itinerary
                "airline_code": firstSegmentData["marketing"]["airlineCode"],
                "airline_name": FlightSearchTools.airline_dict.get(firstSegmentData["marketing"]["airlineCode"], ""),
                "flight_number": firstSegmentData["marketing"]["num"],
                "operating_airline_code": firstSegmentData["operating"]["airlineCode"],
                "operating_flight_number": firstSegmentData["operating"]["num"],
                "duration": flight_duration_iso,  # firstSegmentData["duration"]["iso8601"],
                "flight_duration_in_seconds": flight_duration_in_seconds,
                "aircraft_iata_code": firstSegmentData["equipment"]["code"],
                "aircraft_name": firstSegmentData["equipment"]["name"],
                # note below we carry along the entire set of fare options for
                # the given itinerary
                "price": round(
                    round(totalFare.get("base").get("amount"), 2)
                    + round(totalFare.get("tax").get("amount"), 2)
                    + round(total_seat_price or 0, 2),
                    2,
                ),
                "total_seat_price": total_seat_price,
                "source": itin.get("posInfo", [{}])[0].get("source", ""),
                "currency": totalFare.get("base").get("currencyCode"),
                "base_price": round(totalFare.get("base").get("amount"), 2),
                "base_currency": totalFare.get("base").get("currencyCode"),
                "tax_price": round(totalFare.get("tax").get("amount"), 2),
                "tax_currency": totalFare.get("tax").get("currencyCode"),
                "cabin": firstSegment.get("cabin"),
                "mix_cabins": [segment.get("cabin") for segment in segments],
                "booking_code": firstSegment.get("bookingCode"),
                "mix_booking_codes": [segment.get("bookingCode") for segment in segments],
                "total_distance_miles": total_distance_miles,
                "fare_option_name": leg.get("brandName"),
                # "fare_rules": firstLeg.get('travelerInfos')[0].get('fareRules'),
                "cancellation_policy": fareRules.get("cancellationPolicy", {}).get("description", None),
                "exchange_policy": fareRules.get("exchangePolicy", {}).get("description", None),
                "seat_selection_policy": fareRules.get("seatSelectionPolicy", {}).get("description", None),
                "boarding_policy": fareRules.get("boardingPolicy", {}).get("description", None),
                "checkin_policy": fareRules.get("checkinPolicy", {}).get("description", None),
                "lounge_policy": fareRules.get("loungePolicy", {}).get("description", None),
                "seat_type_policy": fareRules.get("seatType", {}).get("seatType", None),
                "baggage_checkedin_policy": [
                    item.get("description", None) for item in fareRules.get("baggagePolicy", {}).get("checkedIn", [])
                ],
                "baggage_carryon_policy": [
                    item.get("description", None) for item in fareRules.get("baggagePolicy", {}).get("carryOn", [])
                ],
                "number_of_stops": len(segments) - 1,
            }

            # Check if there is more than one flight, if so we're going to store the second flight's data in fs1
            # note that if there were more than 2 segments, we would need to add more fs variables
            # doing it this way allows to keep the structure as flat as
            # possible for potential representation as csv vs json
            all_flight_airline = []
            dedup_keys = []
            for idx, segment in enumerate(segments):
                segmentFlight = segment["flightData"]["flightData"]
                all_flight_airline.append(segmentFlight["marketing"]["airlineCode"])
                flight_option.update(
                    {
                        f"fs{idx}_origin": segmentFlight["origin"]["airportCode"],
                        f"fs{idx}_origin_name": segmentFlight["origin"]["airportName"],
                        f"fs{idx}_destination": segmentFlight["destination"]["airportCode"],
                        f"fs{idx}_destination_name": segmentFlight["destination"]["airportName"],
                        f"fs{idx}_departure_time": segmentFlight["departureDateTime"]["iso8601"],
                        f"fs{idx}_departure_timezone": flight_utils.get_timezone_from_iata_airport_code(
                            segmentFlight["origin"]["airportCode"]
                        ),
                        f"fs{idx}_arrival_timezone": flight_utils.get_timezone_from_iata_airport_code(
                            segmentFlight["destination"]["airportCode"]
                        ),
                        f"fs{idx}_arrival_time": segmentFlight["arrivalDateTime"]["iso8601"],
                        f"fs{idx}_airline_code": segmentFlight["marketing"]["airlineCode"],
                        f"fs{idx}_airline_name": FlightSearchTools.airline_dict.get(
                            segmentFlight["marketing"]["airlineCode"], ""
                        ),
                        f"fs{idx}_flight_number": segmentFlight["marketing"]["num"],
                        f"fs{idx}_operating_airline_code": segmentFlight["operating"]["airlineCode"],
                        f"fs{idx}_operating_flight_number": segmentFlight["operating"]["num"],
                        f"fs{idx}_duration": segmentFlight["duration"]["iso8601"],
                        f"fs{idx}_aircraft_iata_code": segmentFlight["equipment"]["code"],
                        f"fs{idx}_aircraft_name": segmentFlight["equipment"]["name"],
                        f"fs{idx}_cabin": segment.get("cabin"),
                        f"fs{idx}_booking_code": segment.get("bookingCode"),
                    }
                )
                dedup_keys.append(f"{segmentFlight['operating']['airlineCode']}{segmentFlight['operating']['num']}")

            flight_option.update(FlightSearchTools.flatten_flight_attributes(firstSegment))
            flight_option["all_flight_airline"] = all_flight_airline
            flight_option["dedup_key"] = "_".join(dedup_keys)

            flight_options.append(flight_option)

        return flight_options

    @staticmethod
    @atime_function
    async def exchange_search_flights_spotnana(flight_params: str) -> str:
        flight_params_dict = json.loads(flight_params)
        existing_confirmation_id = flight_params_dict["confirmation_id"]

        url = f"{settings.SPOTNANA_HOST}/v2/air/modify-search"

        legs = []
        flight_search_type = "ONE_WAY"
        if flight_params_dict.get("departure_flight_want_change_to") is not None:
            legs.append({"index": "0", "remove": True})
            legs.append(
                {
                    "origin": flight_params_dict.get("departure_flight_want_change_to", {}).get("start_ariport_code"),
                    "destination": flight_params_dict.get("departure_flight_want_change_to", {}).get(
                        "end_airport_code"
                    ),
                    "date": flight_params_dict.get("departure_flight_want_change_to", {}).get("date"),
                }
            )
        else:
            legs.append({"index": "0", "remove": False})

        if flight_params_dict.get("return_flight_want_change_to") is not None:
            if flight_params_dict.get("departure_flight_want_change_to") is not None:
                flight_search_type = "ROUND_TRIP"
            is_newly_add = flight_params_dict.get("return_flight_want_change_to").get("is_newly_add", False)
            if not is_newly_add:
                legs.append({"index": "1", "remove": True})
            legs.append(
                {
                    "origin": flight_params_dict.get("return_flight_want_change_to", {}).get("start_ariport_code"),
                    "destination": flight_params_dict.get("return_flight_want_change_to", {}).get("end_airport_code"),
                    "date": flight_params_dict.get("return_flight_want_change_to", {}).get("date"),
                }
            )
        else:
            if not flight_params_dict.get("is_original_one_way"):
                legs.append({"index": "1", "remove": False})

        leg_index = 0
        if (
            flight_params_dict.get("return_flight_want_change_to") is not None
            and flight_params_dict.get("do_change_return_flight_search", False)
            and not flight_params_dict.get("do_change_outbound_flight_search", False)
        ):
            leg_index = 1

        payload = {
            "pnrId": existing_confirmation_id,
            "legs": legs,
            "filters": [
                # {"changeable": "CHANGEABLE_FLEXIBLE_REFUNDABLE"},
                {"maxNumStops": (0 if flight_params_dict.get("prefers_non_stop", 0) else 1)},
                # {"alliance": {"airlines": preferred_airlines}},
                # {"airlinePref": {"airlinePrefs": airlinePrefs}},
            ],
            "legSearchParams": {
                "searchId": (
                    flight_params_dict.get("change_flight_search_id")
                    if flight_params_dict.get("change_flight_search_id", None) is not None
                    and flight_params_dict.get("selected_change_outbound_flight_id") is not None
                    else ""
                ),
                "selectedRateOptionId": (
                    ""
                    if flight_params_dict.get("selected_change_outbound_flight_id") is None
                    else (flight_params_dict.get("selected_change_outbound_flight_id") if leg_index == 1 else "")
                ),
                "legIndex": leg_index,
                "pageNumber": 1,
                "pageSize": 50,
                "asyncRouteHappy": True,
            },
            "sortOptions": [{"sortBy": "PRICE", "sortOrder": "ASCENDING", "shelfNumber": 0}],
        }

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "modifyflightrequest_spotnana.json",
            "modifyflightresult_spotnana.json",
        )

        # Check if we got any itineraries, redo search if no itineraries found
        if not data.get("itineraryDetails") or not data["itineraryDetails"].get("itineraries"):
            logger.info(
                f"Redo flight modify search as no flight found with filters: {json.dumps(payload['filters'])}",
                mask=flight_log_mask,
            )
            # No results with filters, try again without filters
            payload_of_redo_search = payload.copy()
            payload_of_redo_search["filters"] = [
                # {"changeable": "CHANGEABLE_FLEXIBLE_REFUNDABLE"}
            ]  # remove all filters except changeable so as to avoid basic economy
            data = await FlightSearchTools.acall_spotnana_api(
                f"{settings.SPOTNANA_HOST}/v2/air/modify-search",
                payload_of_redo_search,
                None,
                "modifyflightrequest_no_filters_spotnana.json",
                "modifyflightresult_no_filters_spotnana.json",
            )

            if not data.get("itineraryDetails") or not data["itineraryDetails"].get("itineraries"):
                logger.error(
                    f"Redone flight modify search still found no flight with filters: {json.dumps(payload_of_redo_search['filters'])}",
                    mask=flight_log_mask,
                )
            else:
                logger.info(
                    f"Redone flight modify search found some flights with filters: {json.dumps(payload_of_redo_search['filters'])}",
                    mask=flight_log_mask,
                )

        search_id = data.get("searchId")

        flat_flight_options = []
        error_response = None
        if data.get("metadata") and data["metadata"].get("airlineInfo"):
            for airline in data["metadata"]["airlineInfo"]:
                FlightSearchTools.airline_dict[airline["airlineCode"]] = airline["airlineName"]

        if data.get("itineraryDetails"):
            # create a flight data dictionary so we can quickly reference
            # flight_1, etc.
            flightData_dict = {}
            for flightData in data["itineraryDetails"]["flightData"]:
                flight_ref = flightData.get("flightRef")
                if flight_ref:
                    flightData_dict[flight_ref] = flightData

            # for each itinerary, extract the flights in a flattened format
            for itin in data["itineraryDetails"]["itineraries"]:
                flat_flight_options.extend(FlightSearchTools.flattenFlights_itin(itin, flightData_dict))

            # flat_flight_options = FlightSearchTools.flattenFlights(data, flight_attributes_dict)

            with open("flightresponse_flatmapped.json", "w") as file:
                file.write(json.dumps(flat_flight_options, indent=2))

            search_id = data.get("searchId")
        else:
            error_response = data.get("message")

        json_str = json.dumps(
            {
                "search_id": search_id,
                "flight_choices": flat_flight_options,
                "selected_flights": [],
                "fare_options": [],
                "baggage_prices": [],
                "error_response": error_response,
                "flight_search_type": flight_search_type,
            }
        )

        with open("flightresponse_mapped.json", "w") as file:
            file.write(json_str)

        logger.info(f"""search_id: {search_id}""")

        return json_str

    @staticmethod
    def __resolve_leg(airport_or_city_iata_code: str, is_city_code: bool, is_multi_legs: bool) -> dict[str, Any]:
        if is_city_code:
            if airport_or_city_iata_code in settings.METRO_AREA_CODES:
                if not is_multi_legs:
                    airports = settings.METRO_AREA_CODES[airport_or_city_iata_code]
                    if len(airports) > 5:
                        logger.warning(
                            f"Spotnana doesn't support more than too many airports in metro area {airport_or_city_iata_code}, truncating to 5",
                            mask=flight_log_mask,
                        )
                        airports = airports[:5]

                    return {
                        "multiAirports": {
                            "airports": airports,
                        }
                    }
                return {
                    # spotnana multi leg doesn't support multi airports
                    "airport": settings.METRO_AREA_CODES[airport_or_city_iata_code][0],
                }
            else:
                return {"city": airport_or_city_iata_code}
        else:
            return {"airport": airport_or_city_iata_code}

    @staticmethod
    @atime_function
    async def search_flights_spotnana(
        flight_params: FlightSearchParams,
        current_segment_index: int | None,
        flight_search_type: FlightSearchType,
        selected_outbound_airline_codes: list[str] | None,
        websocket_send_message: Callable[[dict[str, Any]], None] | None = None,
    ) -> dict[str, Any]:
        url = f"{settings.SPOTNANA_HOST}/v2/air/search-flights"

        preferred_airlines = flight_params["preferred_airline_codes"] or []

        if ("HA" in preferred_airlines) != ("AS" in preferred_airlines):
            preferred_airlines = list(set(preferred_airlines + ["AS", "HA"]))

        alliances = list(
            {
                airline_alliance_id_mapping.get(airline)
                for airline in preferred_airlines
                if airline in airline_alliance_id_mapping
            }
        )

        preferred_return_airlines = ""
        if selected_outbound_airline_codes:
            preferred_return_airlines = selected_outbound_airline_codes
            alliances = list(
                {
                    airline_alliance_id_mapping.get(airline)
                    for airline in preferred_return_airlines
                    if airline in airline_alliance_id_mapping
                }
            )

        airlinePrefs = [
            {"legIndex": 0, "airline": preferred_airlines},
            {"legIndex": 1, "airline": preferred_return_airlines or preferred_airlines},
        ]

        origin = FlightSearchTools.__resolve_leg(
            flight_params["departure_airport_code"] or "",
            flight_params["is_departure_iata_city_code"] or False,
            False,
        )

        destination = FlightSearchTools.__resolve_leg(
            flight_params["arrival_airport_code"] or "",
            flight_params["is_arrival_iata_city_code"] or False,
            False,
        )
        legs = []
        if flight_search_type == FlightSearchType.MULTI_CITY:
            legs = []
            for candidate_flight_option in flight_params["search_segments"] or []:
                origin = FlightSearchTools.__resolve_leg(
                    candidate_flight_option.departure_airport_code or "",
                    candidate_flight_option.is_departure_iata_city_code or False,
                    True,
                )
                destination = FlightSearchTools.__resolve_leg(
                    candidate_flight_option.arrival_airport_code or "",
                    candidate_flight_option.is_arrival_iata_city_code or False,
                    True,
                )
                date = {"iso8601": candidate_flight_option.outbound_date}
                legs.append(
                    {
                        "origin": origin,
                        "destination": destination,
                        "date": date,
                    }
                )
        else:
            legs = [
                {
                    "origin": origin,
                    "destination": destination,
                    "date": {"iso8601": flight_params["outbound_date"]},
                },
            ]

            if flight_params.get("return_date"):
                legs.append(
                    {
                        "origin": destination,
                        "destination": origin,
                        "date": {"iso8601": flight_params["return_date"]},
                    }
                )

        payload = {
            "travelers": [
                {
                    "travelerType": "ADULT",
                    "travelerInfo": {"userId": {"id": FlightSearchTools.user_guid}},
                }
            ],
            "legs": legs,
            "filters": [
                # {"changeable": "CHANGEABLE_FLEXIBLE_REFUNDABLE"}, add basic economy back
                {"maxNumStops": (flight_params["number_of_stops"] if flight_params.get("number_of_stops") else 0)},
                {
                    "alliance": {
                        "airlines": preferred_return_airlines or preferred_airlines,
                        "alliances": alliances,
                    }
                },
                {"multiTicketFilter": {"hideMultiTicket": True}},
                {"airlinePref": {"airlinePrefs": airlinePrefs}},
            ],
            # Spotnana supports returnning all flights, but we would like to limit the number of flights returned
            # We can increase this to get more results
            "legSearchParams": {"pageNumber": 1, "pageSize": 50},
            # mimic spotnana website, sort by relevance. looks like pass empty sortOptions is to sort by relevance.
            "sortOptions": [],
        }

        # construct the time range filter
        # Initialize an empty dictionary for the timeRange filter
        timeRangeFilter = {"timeRange": {"timeRanges": []}}

        if flight_params["outbound_departure_time"] and flight_params["outbound_arrival_time"]:
            timeRange = {
                "legIndex": 0,
                "departure": {
                    "min": {"iso8601": flight_params["outbound_departure_time"] + ":00"},
                    "max": {"iso8601": flight_params["outbound_arrival_time"] + ":00"},
                },
            }

            timeRangeFilter["timeRange"]["timeRanges"].append(timeRange)

        if flight_params["return_departure_time"] and flight_params["return_arrival_time"]:
            timeRange = {
                "legIndex": 1,
                "departure": {
                    "min": {"iso8601": flight_params["return_departure_time"] + ":00"},
                    "max": {"iso8601": flight_params["return_arrival_time"]},
                },
            }

            timeRangeFilter["timeRange"]["timeRanges"].append(timeRange)

        # Append the timeRange filter to the payload if there are time
        # ranges specified
        if len(timeRangeFilter["timeRange"]["timeRanges"]) > 0:
            payload["filters"].append(timeRangeFilter)

        # ask for the return flight
        if (
            flight_params["current_step"] == "RETURN_FLIGHT_SEARCH"
            and flight_params["selected_outbound_flight_id"]
            and flight_params["return_date"]
            and flight_params["search_id"]
        ):
            payload["legSearchParams"].update(
                {
                    "searchId": flight_params["search_id"],
                    "selectedRateOptionId": flight_params["selected_outbound_flight_id"] or "",
                    "legIndex": 1,
                }
            )
        elif flight_params["search_id"] and current_segment_index is not None:
            payload["legSearchParams"].update(
                {
                    "searchId": flight_params["search_id"],
                    "selectedRateOptionId": flight_params["selected_outbound_flight_id"] or "",
                    "legIndex": current_segment_index,
                }
            )

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "flightrequest_spotnana.json",
            "flightresult_spotnana.json",
        )

        # Check if we got any itineraries, redo search if no itineraries found
        if not data.get("itineraryDetails") or not data["itineraryDetails"].get("itineraries"):
            if websocket_send_message:
                websocket_send_message(
                    {
                        "type": "flights_skeleton_async",
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": "I didn't find any flights that exactly match your preferences, so I'm searching a bit deeper to find the best options for you ...",
                    }
                )
            logger.info(
                f"Redo flight search as no flight found with filters: {json.dumps(payload['filters'])}",
                mask=flight_log_mask,
            )
            # No results with filters, try again without filters
            payload_of_redo_search = payload.copy()
            payload_of_redo_search["filters"] = [
                {"changeable": "CHANGEABLE_FLEXIBLE_REFUNDABLE"}
            ]  # remove all filters except changeable so as to avoid basic economy
            data = await FlightSearchTools.acall_spotnana_api(
                f"{settings.SPOTNANA_HOST}/v2/air/search-flights",
                payload_of_redo_search,
                None,
                "flightrequest_no_filters_spotnana.json",
                "flightresult_no_filters_spotnana.json",
            )
            if not data.get("itineraryDetails") or not data["itineraryDetails"].get("itineraries"):
                logger.error(
                    f"Redone flight search still found no flight with filters: {json.dumps(payload_of_redo_search['filters'])}",
                    mask=flight_log_mask,
                )
            else:
                logger.info(
                    f"Redone flight search found some flights with filters: {json.dumps(payload_of_redo_search['filters'])}",
                    mask=flight_log_mask,
                )
        if len(data["itineraryDetails"]["itineraries"]) > 0:
            if websocket_send_message:
                websocket_send_message(
                    {
                        "type": "flights_skeleton_async",
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": f"I found {len(data['itineraryDetails']['itineraries'])} available flight options. I'm now picking the best options for you based on your preferences...",
                    }
                )
        else:
            if websocket_send_message:
                websocket_send_message(
                    {
                        "type": "flights_skeleton_async",
                        "isBotMessage": True,
                        "expectResponse": False,
                        "text": "Sorry but I couldn't find any available flight options.",
                    }
                )

        search_id = data.get("searchId")

        flat_flight_options = []
        search_id = None
        error_response = None
        if data.get("metadata") and data["metadata"].get("airlineInfo"):
            for airline in data["metadata"]["airlineInfo"]:
                FlightSearchTools.airline_dict[airline["airlineCode"]] = airline["airlineName"]

        if data.get("itineraryDetails"):
            # create a flight data dictionary so we can quickly reference
            # flight_1, etc.
            flightData_dict = {}
            for flightData in data["itineraryDetails"]["flightData"]:
                flight_ref = flightData.get("flightRef")
                if flight_ref:
                    flightData_dict[flight_ref] = flightData

            logger.info(f"Found {len(flightData_dict.keys())} flights.", mask=flight_log_mask)
            # for each itinerary, extract the flights in a flattened format
            for itin in data["itineraryDetails"]["itineraries"]:
                flat_flight_options.extend(FlightSearchTools.flattenFlights_itin(itin, flightData_dict))

            # flat_flight_options = FlightSearchTools.flattenFlights(data, flight_attributes_dict)

            with open("flightresponse_flatmapped.json", "w") as file:
                file.write(json.dumps(flat_flight_options, indent=2))

            search_id = data.get("searchId")
        else:
            error_response = data.get("message")
            logger.error(f"No flight found with error: {error_response}", mask=flight_log_mask)

        # Sort by the preferred airline order
        # else len(preferred_airlines),
        # datetime.fromisoformat(x[1]['departure_time'])))
        if not preferred_airlines:
            sorted_flights = sorted(
                flat_flight_options,
                key=lambda x: datetime.fromisoformat(x["departure_time"]),
            )
        else:
            sorted_flights = sorted(
                flat_flight_options,
                key=lambda x: (
                    (
                        preferred_airlines.index(x["airline_code"])
                        if x["airline_code"] in preferred_airlines
                        else len(preferred_airlines)
                    ),
                    datetime.fromisoformat(x["departure_time"]),
                ),
            )

        response_dict = {
            "search_id": search_id,
            "flight_choices": sorted_flights,
            "error_response": error_response,
            "flight_search_type": flight_search_type.value,
        }

        with open("flightresponse_mapped.json", "w") as file:
            file.write(json.dumps(response_dict))

        logger.info(f"""search_id: {search_id}""", mask=flight_log_mask)
        return response_dict

    @staticmethod
    async def get_user_spotnana(userId: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v2/users/{userId}"
        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            response_filename="get_user_spotnana.json",
        )

        return response

    @staticmethod
    async def query_user_spotnana(email: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v2/users"

        params = {
            "companyId": settings.SPOTNANA_COMPANY_GUID,
            "email": email,
            "includeInactive": "false",
        }
        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            params=params,
            request_filename="travelersearch_request_spotnana.json",
            response_filename="travelersearch_response_spotnana.json",
        )

        return response

    @staticmethod
    async def traveler_search_spotnana(email: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v1/traveler/search"
        payload = {
            "organizationId": {
                "id": settings.SPOTNANA_COMPANY_GUID,
            },
            "email": email,
            "includeInactive": False,
        }

        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            data=payload,
            request_filename="travelersearch_request_spotnana.json",
            response_filename="travelersearch_response_spotnana.json",
        )

        return response

    @staticmethod
    async def traveler_read_spotnana(user_org_id: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v1/traveler/read"
        payload = {
            "userOrgId": user_org_id,
            "includeInactive": False,
        }

        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            data=payload,
            request_filename="travelerread_request_spotnana.json",
            response_filename="travelerread_response_spotnana.json",
        )

        return response

    @staticmethod
    async def get_selected_itinerary_spotnana(searchId: str, itineraryId: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v2/air/selected-itinerary"
        payload = {
            "searchId": searchId,
            "itineraryId": itineraryId,
        }

        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            data=payload,
            request_filename="getselecteditin_request_spotnana.json",
            response_filename="getselecteditin_response_spotnana.json",
        )

        return response

    @staticmethod
    async def flight_checkout_spotnana(searchId: str, itineraryId: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v2/air/flight-checkout"
        payload = {
            "searchId": searchId,
            "itineraryId": itineraryId,
        }

        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            data=payload,
            request_filename="flightcheckout_request_spotnana.json",
            response_filename="flightcheckout_response_spotnana.json",
        )

        return response

    @staticmethod
    async def seat_map_spotnana(
        searchId: str | None, itineraryId: str | None, loyaltyInfos: Any, user_guid: Any, pnrId: str | None = None
    ) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v2/air/seat-map"
        itinerary = None
        if pnrId is not None:
            itinerary = {
                "pnr": {
                    "pnrId": pnrId,
                }
            }
        else:
            itinerary = {"airItineraryId": {"searchId": searchId, "itineraryId": itineraryId}}

        payload = {
            "itinerary": itinerary,
            "travelerInfos": [
                {
                    "travelerType": "ADULT",
                    "travelerAge": {"numYears": 22},
                    "travelerInfo": {"userId": {"id": user_guid}},
                    "loyaltyInfos": loyaltyInfos,
                }
            ],
        }

        response = await FlightSearchTools.acall_spotnana_api(
            url=url,
            data=payload,
            request_filename="seatmap_request_spotnana.json",
            response_filename="seatmap_response_spotnana.json",
        )

        return response

    @staticmethod
    async def initiate_booking_spotnana(
        checkoutResponseId: str,
        seatmapResponseId: str,
        fareInfo: Any,
        user: Any,
        user_guid: Any,
        payment_source_id: Any,
        seats: Any,
        seats_price: float,  # spotnana confirmed we shouldn't add seats price to total fare for now.
    ) -> Dict[str, Any]:
        url = f"{settings.SPOTNANA_HOST}/v2/air/initiate-booking"
        totalFareInfo = fareInfo["totalFare"]["base"].copy()
        totalFareInfo["amount"] = fareInfo["totalFare"]["base"]["amount"] + fareInfo["totalFare"]["tax"]["amount"]
        totalFareInfo["convertedAmount"] = (
            fareInfo["totalFare"]["base"]["convertedAmount"] + fareInfo["totalFare"]["tax"]["convertedAmount"]
        )

        personalInfo = user.get("personalInfo", {})
        businessInfo = user.get("businessInfo", {})
        identityDocs = personalInfo.get("identityDocs", [])

        payload = {
            "initiateBookingWorkflowIds": {
                "checkoutResponseId": checkoutResponseId,
                "seatMapResponseId": seatmapResponseId,
            },
            "travelers": [
                {
                    "travelerDetails": {
                        "travelerId": {"id": user_guid},
                        "travelerInfo": {"userId": {"id": user_guid}},
                        "travelerType": "ADULT",
                        "title": personalInfo.get("title", None),
                        "name": personalInfo.get("name", ""),
                        "gender": personalInfo.get("gender", ""),
                        "dob": personalInfo.get("dob", ""),
                        "phoneNumber": (
                            personalInfo.get("phoneNumbers", [None])[0] if personalInfo.get("phoneNumbers") else None
                        ),
                        "email": businessInfo.get("email", ""),
                        "address": (
                            personalInfo.get("addresses", [None])[0] if personalInfo.get("addresses") else None
                        ),
                        "identityDocs": identityDocs,
                    },
                    "seats": seats if seats else [],
                    "baggages": [],
                    "ancillaries": [],
                    "shareContactInfo": False,
                }
            ],
            "bookingCharges": [
                {
                    "amount": fareInfo["totalFare"],
                    "paymentMethod": {
                        "selectedPaymentSource": {
                            "paymentSourceId": payment_source_id,
                            "postPaymentRedirectionUrl": "",
                            "cvv": "",
                            "amount": totalFareInfo,
                        }
                    },
                }
            ],
            "useExistingBooking": False,
        }

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "initiatebooking_request_spotnana.json",
            "initiatebooking_response_spotnana.json",
        )

        return data

    @staticmethod
    async def create_trip_spotnana(flight_params: str, user_guid: Any) -> Any:
        flight_params_dict = json.loads(flight_params)

        trip_uuid = uuid.uuid4()
        url = f"{settings.SPOTNANA_HOST}/v2/trips"
        arrival_airport = flight_params_dict.get("arrival_airport_code") or "Unknown"
        desc = arrival_airport + " - " + str(trip_uuid)
        payload = {
            "tripName": desc,
            "tripDescription": desc,
            "userId": {"id": user_guid},
            "registrarId": {"id": user_guid},
        }

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "createtrip_request_spotnana.json",
            "createtrip_response_spotnana.json",
        )

        return data

    @staticmethod
    async def get_trip_details_spotnana(trip_id: str) -> Any:
        url = f"{settings.SPOTNANA_HOST}/v3/trips/{trip_id}/detail"

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            None,
            None,
            "tripdetails_request_spotnana.json",
            "tripdetails_response_spotnana.json",
        )

        return data

    @staticmethod
    def flatten_flights_details_in_trip(trip_details: Dict[str, Any]) -> List[Dict[str, Any]]:
        flights = trip_details.get("pnrs", [{}])[0].get("data", {}).get("airPnr", {})
        airport_code_to_name_map = {
            info["airportCode"]: info["airportName"]
            for info in trip_details.get("pnrs", [{}])[0]
            .get("data", {})
            .get("additionalMetadata", {})
            .get("airportInfo", [{}])
        }
        airline_code_to_name_map = {
            info["airlineCode"]: info["airlineName"]
            for info in trip_details.get("pnrs", [{}])[0]
            .get("data", {})
            .get("additionalMetadata", {})
            .get("airlineInfo", [{}])
        }

        seats = []

        travelers_info = flights.get("travelerInfos", [])
        if len(travelers_info) > 0:
            seats = travelers_info[0].get("booking", {}).get("seats", [])

        flight_options = []
        for leg_idnex, leg in enumerate(flights.get("legs")):
            totalFare = trip_details.get("tripPaymentInfo", {}).get("totalFareAmount", {})

            for flight_index, flight in enumerate(leg.get("flights")):
                seat = list(
                    filter(lambda x: x.get("legIdx") == leg_idnex and x.get("flightIdx") == flight_index, seats)
                )
                flight_option = {
                    "origin": flight["origin"],
                    "origin_name": airport_code_to_name_map[flight["origin"]],
                    "destination": flight["destination"],
                    "destination_name": airport_code_to_name_map[flight["destination"]],
                    "departure_time": flight["departureDateTime"]["iso8601"],
                    "arrival_time": flight["arrivalDateTime"]["iso8601"],
                    "airline_code": flight["marketing"]["airlineCode"],
                    "airline_name": airline_code_to_name_map[flight["marketing"]["airlineCode"]],
                    "flight_number": flight["marketing"]["num"],
                    "duration": flight["duration"]["iso8601"],
                    "cancelled": flight.get("flightStatus", "UNKNOWN") == "CANCELLED",
                    "aircraft_iata_code": flight.get("equipment", {}).get("code"),
                    "aircraft_name": flight.get("equipment", {}).get("name"),
                    "currency": totalFare.get("base").get("currencyCode"),
                    "base_price": round(totalFare.get("base").get("amount"), 2),
                    "base_currency": totalFare.get("base").get("currencyCode"),
                    "tax_price": round(totalFare.get("tax").get("amount"), 2),
                    "tax_currency": totalFare.get("tax").get("currencyCode"),
                    "cabin": flight.get("cabin"),
                    "old_seat": seat[0].get("number")
                    if len(seat) == 1 and seat[0].get("status") == "CONFIRMED"
                    else None,
                }

                flight_options.append(flight_option)

        return flight_options

    @staticmethod
    def get_vendor_confirmation_number(trip_details: dict[str, Any]) -> str | None:
        pnrs = trip_details.get("pnrs", [])
        if not pnrs:
            return None

        first_pnr = pnrs[0]
        data = first_pnr.get("data", {})
        airpnr = data.get("airPnr", {})
        legs = airpnr.get("legs", [])
        if not legs:
            return None

        first_leg = legs[0]
        flights = first_leg.get("flights", [])
        if not flights:
            return None

        first_flight = flights[0]
        vendor_confirmation_number = first_flight.get("vendorConfirmationNumber")

        return vendor_confirmation_number

    @staticmethod
    async def validate_itinerary_spotnana(
        checkoutResponseId: str,
        initiateBookingId: str | None,
        tripId: str,
        seatmapResponseId: str | None,
        user: Any,
        fareInfo: Any,
        user_guid: Any,
        user_email: str | None,
        payment_source_id: Any,
        seats_price: float,  # spotnana confirmed we shouldn't add seats price to total fare for now.
        credits_ticket_number: str | None = None,
        flight_ids: list | None = None,
        seats: Any = None,
    ) -> Dict[str, Any]:
        url = f"{settings.SPOTNANA_HOST}/v2/air/revalidate-itinerary"
        totalFareInfo = fareInfo["totalFare"]["base"].copy()
        totalFareInfo["amount"] = fareInfo["totalFare"]["base"]["amount"] + fareInfo["totalFare"]["tax"]["amount"]
        totalFareInfo["convertedAmount"] = (
            fareInfo["totalFare"]["base"]["convertedAmount"] + fareInfo["totalFare"]["tax"]["convertedAmount"]
        )

        personalInfo = user.get("personalInfo", {})
        businessInfo = user.get("businessInfo", {})
        identityDocs = personalInfo.get("identityDocs", [])

        payload = {
            "workflowIds": {
                "checkoutResponseId": checkoutResponseId,
                "seatMapResponseId": seatmapResponseId if seatmapResponseId else None,
                "paymentSetupResponseId": "",
                "initiateBookingId": (initiateBookingId if initiateBookingId is not None else None),
            },
            "tripId": {"id": tripId},
            "travelers": [
                {
                    "travelerDetails": {
                        "travelerId": {"id": user_guid},
                        "travelerInfo": {"userId": {"id": user_guid}},
                        "travelerType": "ADULT",
                        "title": personalInfo.get("title", ""),
                        "name": personalInfo.get("name", ""),
                        "gender": personalInfo.get("gender", ""),
                        "dob": personalInfo.get("dob", ""),
                        "phoneNumber": (
                            personalInfo.get("phoneNumbers", [None])[0] if personalInfo.get("phoneNumbers") else None
                        ),
                        "email": businessInfo.get("email", ""),
                        "address": (
                            personalInfo.get("addresses", [None])[0] if personalInfo.get("addresses") else None
                        ),
                        "identityDocs": identityDocs,
                    },
                    "seats": seats if seats else [],
                    "baggages": [],
                    "ancillaries": [],
                    "shareContactInfo": False,
                }
            ],
            "bookingCharges": [
                {
                    "amount": fareInfo["totalFare"],
                    "paymentMethod": {
                        "selectedPaymentSource": {
                            "paymentSourceId": payment_source_id,
                            "postPaymentRedirectionUrl": "",
                            "cvv": "",
                            "amount": totalFareInfo,
                        }
                    },
                }
            ],
        }
        if credits_ticket_number:
            await FlightSearchTools._apply_flight_credits(
                payload, user_guid, user_email, credits_ticket_number, flight_ids
            )
        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "validateitin_request_spotnana.json",
            "validateitin_response_spotnana.json",
        )

        return data

    @staticmethod
    async def create_pnr_spotnana(bookingId: str | None, initiateBookingId: str | None, tripId: str | None) -> Any:
        # using undocumented endpoint that supports hold, found by inspecting
        # their webapp
        url = f"{settings.SPOTNANA_HOST}/v1/air-create-pnr"

        payload = {
            "bookingId": bookingId,
            "initiateBookingId": initiateBookingId,
            # interesting, could be used for postback checking
            # "ccPostVerificationUrl": "https://sboxmeta-app.partners.spotnana.com/flights/check-pnr-status?session-id=b5fcef7a-a536-4bbb-bc1f-6f4bfb58b270",
            # "holdInfo": {
            #     "holdStatus": 2 # no idea what 2 means but I got it from inspecting their app
            # },
            "tripData": {"tripId": {"id": tripId}, "isTestBooking": False},
        }

        data = await FlightSearchTools.acall_spotnana_api(
            url,
            payload,
            None,
            "createpnr_request_spotnana.json",
            "createpnr_response_spotnana.json",
        )
        data.update({"trip_id": tripId})

        return data

    @staticmethod
    async def exchangeflight_validation_spotnana(
        flight_params: str,
        user: User,
        seat_selections: list[SeatSelectionForFlight] | None,
        admin_user: User | UserDB | None = None,
    ) -> str:
        flight_params_dict = json.loads(flight_params)

        traveler_tasks = [FlightSearchTools.get_spotnana_traveler_search_and_read(user.email)]
        if admin_user:
            traveler_tasks.append(FlightSearchTools.get_spotnana_traveler_search_and_read(admin_user.email))
        (traveler_search, traveler_read), *rest = await asyncio.gather(*traveler_tasks)
        (_, admin_traveler_read) = rest[0] if admin_user else (None, None)

        is_updated, display_payment_profile_form = await FlightSearchTools.check_user_payment_profile(
            traveler_search, user, admin_user
        )
        if is_updated:
            traveler_search = await FlightSearchTools.traveler_search_spotnana(user.email)

        if display_payment_profile_form:
            return json.dumps(
                {
                    "error_response": "No payment profile found",
                    "status": "error_no_flight_payment_profile_found",
                }
            )

        # use the first traveler found, assuming that we only create one
        # traveler profile per email from otto to spotnana
        payment_source_id = SpotnanaHelper.get_payment_source_id(traveler_read, admin_traveler_read)
        user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")

        # unfortunately we have to make this call to get properly formatted
        # user info, ex. title in a string vs. a numeric enum for later use
        # during booking
        spotnana_user: dict[str, Any] = await FlightSearchTools.get_user_spotnana(user_guid)
        # Spotnana doesn't allow special characters during booking, trim leading and trailing spaces
        FlightSearchTools.trim_user_name_fields(spotnana_user)

        itinerary_id = flight_params_dict.get("selected_change_return_flight_id") or flight_params_dict.get(
            "selected_change_outbound_flight_id"
        )

        search_id = flight_params_dict.get("search_id")

        selected_itin = await FlightSearchTools.get_selected_itinerary_spotnana(
            searchId=search_id, itineraryId=itinerary_id
        )
        checkout = await FlightSearchTools.flight_checkout_spotnana(searchId=search_id, itineraryId=itinerary_id)
        loyalty_infos = []
        stored_freq_flyer_ids = {}
        user_profile_flights_loyalty_programs: dict[str, Any] | None = await get_user_profile_flights_loyalty_programs(
            flight_params_dict.get("user_id")
        )
        if user_profile_flights_loyalty_programs is not None:
            stored_freq_flyer_ids = (
                {
                    program["IATACode"]: program["number"]
                    for program in user_profile_flights_loyalty_programs.get("loyaltyPrograms", [])
                }
                if user_profile_flights_loyalty_programs.get("loyaltyPrograms", []) is not None
                else {}
            )

        flights = FlightSearchTools.map_flights_in_itin(selected_itin.get("itinerary", {}))

        if len(stored_freq_flyer_ids) > 0:
            for current_flight in flights:
                outbound_freq_flyer_number_airline = FlightSearchTools.freq_flyer_airline_code(
                    current_flight.get("airline_code"), stored_freq_flyer_ids
                )

                if outbound_freq_flyer_number_airline in stored_freq_flyer_ids:
                    flight_id = current_flight.get("flight_id")
                    loyalty_infos.append(
                        {
                            "flightId": flight_id,
                            "loyaltyInfos": [
                                {
                                    "id": stored_freq_flyer_ids[outbound_freq_flyer_number_airline],
                                    "issuedBy": outbound_freq_flyer_number_airline,
                                    "type": "AIR",
                                }
                            ],
                        }
                    )
        seat_map = await FlightSearchTools.seat_map_spotnana(
            searchId=search_id,
            itineraryId=itinerary_id,
            loyaltyInfos=loyalty_infos,
            user_guid=user_guid,
        )
        exsiting_trip_id = flight_params_dict.get("trip_id")

        flights = FlightSearchTools.map_flights_in_itin(selected_itin.get("itinerary", {}))
        final_seats = []
        seat_validation_messages = []
        updated_seat_selection = {}
        seats_price = 0.0

        for index, flight in enumerate(flights):
            flight_id = flight.get("flight_id")

            original_seat_selection = None
            for original_seat in seat_selections or []:
                if original_seat.start_airport == flight.get("origin", {}).get(
                    "airportCode"
                ) and original_seat.end_airport == flight.get("destination", {}).get("airportCode"):
                    original_seat_selection = original_seat
                    break

            if not original_seat_selection:
                continue

            original_seat = original_seat_selection.seat_number

            flight_seat_map_ids = (
                seat_map.get("travelerSeatMaps", [])[0].get("flightSeatMapIds", [])
                if len(seat_map.get("travelerSeatMaps", [])) > 0
                else []
            )

            if index >= len(flight_seat_map_ids):
                seat_validation_messages.append(
                    f"Your seat {original_seat} for flight {flight_id} is expired. We will proceed without seat selection. you can select seat after booking."
                )
                continue

            flight_seat_map_id = flight_seat_map_ids[index]
            seat_map_list = [
                seat for seat in seat_map.get("seatMaps", []) if seat.get("seatMapId") == flight_seat_map_id
            ]

            if not seat_map_list or not seat_map_list[0].get("cabinSections", []):
                seat_validation_messages.append(
                    f"Your seat {original_seat} for flight {flight_id} is expired. We will proceed without seat selection. you can select seat after booking."
                )
                continue

            _, available_seats = FlightSearchTools.convert_seat_map_to_csv(seat_map_list[0])

            seat_still_available = False
            if original_seat:
                seat_still_available = original_seat in available_seats

            if seat_still_available:
                final_seats.append(
                    {
                        "flightId": flight_id,
                        "seatNumbers": [original_seat],
                    }
                )
                updated_seat_selection[flight_id] = original_seat
                seats_price += available_seats.get(original_seat) or 0.0
            else:
                seat_validation_messages.append(
                    f"Your seat {original_seat} for flight {flight_id} is expired. We will proceed without seat selection. you can select seat after booking."
                )

        # TODO: whether to apply flight credits?
        validate_itin = await FlightSearchTools.validate_itinerary_spotnana(
            checkoutResponseId=checkout.get("checkoutResponseId"),
            initiateBookingId=None,
            tripId=exsiting_trip_id,
            seatmapResponseId=seat_map.get("seatMapResponseId"),
            user=spotnana_user,
            fareInfo=selected_itin.get("itinerary").get("fareInfo"),
            user_guid=user_guid,
            user_email=user.email,
            payment_source_id=payment_source_id,
            seats=final_seats,
            seats_price=seats_price,
        )

        flat_flight_options = FlightSearchTools.flattenFlights_itin(selected_itin["itinerary"], None)
        validate_itin.update({"selected_flights": flat_flight_options})

        booking_id = validate_itin.pop("bookingId")
        validate_itin["valid_change_booking_id"] = booking_id

        if updated_seat_selection:
            validate_itin.update({"updated_seat_selection": updated_seat_selection})
        if seat_validation_messages:
            validate_itin.update({"seat_validation_messages": seat_validation_messages})

        json_str = json.dumps(validate_itin)
        return json_str

    @staticmethod
    def only_airlines_missing_freq_flyer_id(operating_carriers, stored_freq_flyer_ids) -> List[str]:
        missing_freq_flyer_airlines = []
        for carrier_code in operating_carriers:
            # exact match found
            if stored_freq_flyer_ids.get(carrier_code) and len(stored_freq_flyer_ids[carrier_code]) > 0:
                continue
            # alliance check
            matched_alliance = False
            for stored_airline_code in stored_freq_flyer_ids:
                if carrier_code in (airline_allies_mapping.get(stored_airline_code) or []):
                    matched_alliance = True
                    break
            # alliance found
            if matched_alliance:
                continue
            missing_freq_flyer_airlines.append(carrier_code)
        return missing_freq_flyer_airlines

    @staticmethod
    def freq_flyer_airline_code(booked_airline_code: str | None, stored_freq_flyer_ids: dict[str, str]) -> str | None:
        if booked_airline_code is None:
            return None

        if booked_airline_code in stored_freq_flyer_ids.keys():
            return booked_airline_code

        for airline_code in airline_allies_mapping.get(booked_airline_code, []):
            if airline_code in stored_freq_flyer_ids.keys():
                return airline_code

        return None

    # It is used in FOH agent.
    @staticmethod
    @retry(
        retry=retry_if_exception_type(SpotnanaRetryableException),
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=1, max=4),
    )
    async def flight_validation_spotnana_simplified(
        flight_params_dict: dict[str, Any],
        user: User,
        admin_user: User | UserDB | None = None,
        websocket_send_message: partial[Coroutine[Any, Any, None]] | None = None,
    ) -> tuple[str, Any | None, Any | None, Any]:
        try:
            user_email = user.email
            user_id = user.id
            search_id = flight_params_dict.get("search_id")
            itinerary_id = flight_params_dict.get("selected_return_flight_id") or flight_params_dict.get(
                "selected_outbound_flight_id"
            )
            assert (
                user_email is not None and user_id is not None and search_id is not None and itinerary_id is not None
            ), "User id/email, search_id and itinerary_id are required for flight validation"

            admin_user_email = admin_user.email if admin_user else None

            traveler_tasks = [FlightSearchTools.get_spotnana_traveler_search_and_read(user_email)]
            if admin_user_email:
                traveler_tasks.append(FlightSearchTools.get_spotnana_traveler_search_and_read(admin_user_email))
            (traveler_search, traveler_read), *rest = await asyncio.gather(*traveler_tasks)
            (_, admin_traveler_read) = rest[0] if admin_user_email else (None, None)

            is_updated, display_payment_profile_form = await FlightSearchTools.check_user_payment_profile(
                traveler_search, user, admin_user
            )
            if is_updated:
                traveler_search = await FlightSearchTools.traveler_search_spotnana(user.email)

            if display_payment_profile_form:
                return (
                    json.dumps(
                        {
                            "error_response": "No payment profile found",
                            "status": "error_no_flight_payment_profile_found",
                            "missing_freq_flyer_airlines": flight_params_dict.get("selected_airline_codes", None),
                        }
                    ),
                    None,
                    None,
                    None,
                )

            # use the first traveler found, assuming that we only create one
            # traveler profile per email from otto to spotnana
            payment_source_id = SpotnanaHelper.get_payment_source_id(traveler_read, admin_traveler_read)
            user_guid = traveler_read.get("traveler", {}).get("userOrgId", {}).get("userId", {}).get("id")

            # unfortunately we have to make this call to get properly formatted
            # user info, ex. title in a string vs. a numeric enum for later use
            # during booking
            spotnana_user: dict[str, Any] = await FlightSearchTools.get_user_spotnana(user_guid)
            # Spotnana doesn't allow special characters during booking, trim leading and trailing spaces
            FlightSearchTools.trim_user_name_fields(spotnana_user)

            selected_itin = await FlightSearchTools.get_selected_itinerary_spotnana(
                searchId=search_id, itineraryId=itinerary_id
            )

            flights = FlightSearchTools.map_flights_in_itin(selected_itin.get("itinerary", {}))

            all_flight_ids = [flight.get("flight_id") for flight in flights]

            checkout = await FlightSearchTools.flight_checkout_spotnana(searchId=search_id, itineraryId=itinerary_id)
            loyaltyInfos = []
            stored_freq_flyer_ids = {}
            user_profile_flights_loyalty_programs: (
                dict[str, Any] | None
            ) = await get_user_profile_flights_loyalty_programs(user_id)

            if user_profile_flights_loyalty_programs is not None:
                stored_freq_flyer_ids = (
                    {
                        program["IATACode"]: program["number"]
                        for program in user_profile_flights_loyalty_programs.get("loyaltyPrograms", [])
                    }
                    if user_profile_flights_loyalty_programs.get("loyaltyPrograms", []) is not None
                    else {}
                )

            if len(stored_freq_flyer_ids) > 0:
                for current_flight in flights:
                    outbound_freq_flyer_number_airline = FlightSearchTools.freq_flyer_airline_code(
                        current_flight.get("airline_code"), stored_freq_flyer_ids
                    )

                    if outbound_freq_flyer_number_airline in stored_freq_flyer_ids:
                        flight_id = current_flight.get("flight_id")
                        loyaltyInfos.append(
                            {
                                "flightId": flight_id,
                                "loyaltyInfos": [
                                    {
                                        "id": stored_freq_flyer_ids[outbound_freq_flyer_number_airline],
                                        "issuedBy": outbound_freq_flyer_number_airline,
                                        "type": "AIR",
                                    }
                                ],
                            }
                        )
            seat_map = await FlightSearchTools.seat_map_spotnana(
                searchId=search_id,
                itineraryId=itinerary_id,
                loyaltyInfos=loyaltyInfos,
                user_guid=user_guid,
            )

            original_seats = flight_params_dict.get("seat_selection") or []

            final_seats = []
            seat_validation_messages = []
            updated_seat_selection = {}

            seats_price = 0.0

            for index, flight in enumerate(flights):
                flight_id = flight.get("flight_id")
                original_seat_selection = None
                for original_seat in original_seats:
                    if original_seat.get("start_airport") == flight.get("origin", {}).get(
                        "airportCode"
                    ) and original_seat.get("end_airport") == flight.get("destination", {}).get("airportCode"):
                        original_seat_selection = original_seat
                        break

                if not original_seat_selection:
                    continue

                original_seat = original_seat_selection.get("seat_number")

                flight_seat_map_ids = (
                    seat_map.get("travelerSeatMaps", [])[0].get("flightSeatMapIds", [])
                    if len(seat_map.get("travelerSeatMaps", [])) > 0
                    else []
                )

                if index >= len(flight_seat_map_ids):
                    seat_validation_messages.append(
                        f"Your seat {original_seat} for flight {flight_id} is expired. We will proceed without seat selection. you can select seat after booking."
                    )
                    continue

                flight_seat_map_id = flight_seat_map_ids[index]
                seat_map_list = [
                    seat for seat in seat_map.get("seatMaps", []) if seat.get("seatMapId") == flight_seat_map_id
                ]

                if not seat_map_list or not seat_map_list[0].get("cabinSections", []):
                    seat_validation_messages.append(
                        f"Your seat {original_seat} for flight {flight_id} is expired. We will proceed without seat selection. you can select seat after booking."
                    )
                    continue

                _, available_seats = FlightSearchTools.convert_seat_map_to_csv(seat_map_list[0])

                seat_still_available = False
                if original_seat:
                    seat_still_available = original_seat in available_seats

                if seat_still_available:
                    final_seats.append(
                        {
                            "flightId": flight_id,
                            "seatNumbers": [original_seat],
                        }
                    )
                    updated_seat_selection[flight_id] = original_seat
                    seats_price += available_seats.get(original_seat) or 0.0
                else:
                    if websocket_send_message:
                        await websocket_send_message(
                            message={
                                "type": "search_update",
                                "isBotMessage": True,
                                "expectResponse": False,
                                "text": f"Your previously selected seat: {original_seat} is no longer available for {original_seat_selection.get('airline_code')}:{original_seat_selection.get('flight_number')}. We will proceed without seat selection. you can select seat after booking.",
                            }
                        )
                    seat_validation_messages.append(
                        f"Your seat {original_seat} for flight {flight_id} is expired. We will proceed without seat selection. you can select seat after booking."
                    )

            initiate_booking = await FlightSearchTools.initiate_booking_spotnana(
                checkoutResponseId=checkout.get("checkoutResponseId"),
                seatmapResponseId=seat_map.get("seatMapResponseId"),
                user=spotnana_user,
                fareInfo=selected_itin.get("itinerary").get("fareInfo"),
                user_guid=user_guid,
                payment_source_id=payment_source_id,
                seats=final_seats,
                seats_price=seats_price,
            )
            create_trip = await FlightSearchTools.create_trip_spotnana(
                json.dumps(flight_params_dict), user_guid=user_guid
            )
            validate_itin = await FlightSearchTools.validate_itinerary_spotnana(
                checkoutResponseId=checkout.get("checkoutResponseId"),
                initiateBookingId=initiate_booking.get("initiateBookingResponseId"),
                tripId=create_trip.get("id"),
                seatmapResponseId=seat_map.get("seatMapResponseId"),
                user=spotnana_user,
                fareInfo=selected_itin.get("itinerary").get("fareInfo"),
                user_guid=user_guid,
                user_email=user_email,
                payment_source_id=payment_source_id,
                seats=final_seats,
                credits_ticket_number=flight_params_dict.get("credits_ticket_number"),
                flight_ids=all_flight_ids,
                seats_price=seats_price,
            )

            flat_flight_options = FlightSearchTools.flattenFlights_itin(selected_itin["itinerary"], None)
            validate_itin.update({"selected_flights": flat_flight_options})

            if updated_seat_selection:
                validate_itin.update({"updated_seat_selection": updated_seat_selection})
            if seat_validation_messages:
                validate_itin.update({"seat_validation_messages": seat_validation_messages})

            json_str = json.dumps(validate_itin)
            return (
                json_str,
                validate_itin.get("bookingId"),
                initiate_booking.get("initiateBookingResponseId"),
                create_trip.get("id"),
            )

        except Exception as e:
            logger.error(f"Error in flight validation: {e}", mask=flight_log_mask)
            if (
                isinstance(e, HTTPException)
                and e.detail
                and isinstance(e.detail, dict)
                and e.detail.get("errorCode") == AgentErrorCode.ITINERARY_FARE_EXPIRED.name
            ):
                return (
                    json.dumps(
                        {
                            "error_response": "spotnana itinerary fare expired",
                            "status": AgentErrorCode.ITINERARY_FARE_EXPIRED.name,
                        }
                    ),
                    None,
                    None,
                    None,
                )

            if (
                isinstance(e, HTTPException)
                and e.detail
                and isinstance(e.detail, dict)
                and e.detail.get("errorCode") == AgentErrorCode.RETRYABLE_THIRDPARTY_ERROR.name
            ):
                raise SpotnanaRetryableException("spotnana retryable error occurred") from e

            raise e

    @staticmethod
    async def _apply_flight_credits(payload, user_guid, user_email, credits_ticket_number, flight_ids):
        user_unused_credits = await flight_credits_api.get_user_unused_credits(user_email)
        credits_info = [
            credits_info
            for credits_info in user_unused_credits
            if credits_info.get("ticket_number") == credits_ticket_number
        ]

        if not credits_info:
            raise Exception(f"Credit ticket number {credits_ticket_number} not found.")
        credits_info = credits_info[0].get("extra", {})

        if not (bookingCharges := payload.get("bookingCharges")):
            return
        if not (payment_source := bookingCharges[0].get("paymentMethod").get("selectedPaymentSource")):
            raise Exception(f"Could not apply flight credits: {payload}")

        payment_source["rawPaymentSource"] = {
            "type": "CREDIT",
            "pnrOwningPcc": credits_info.get("owningPcc"),
            "unusedCreditPcc": credits_info.get("pcc"),
            "departureCountry": credits_info.get("departureCountry"),
            "arrivalCountry": credits_info.get("arrivalCountry"),
            "ticketType": credits_info.get("ticketType"),
            "departureDate": credits_info.get("departureDate"),
            "segmentsAvailable": credits_info.get("segmentsAvailable"),
            "traveler": {"userId": {"id": user_guid}},
            "passengerName": credits_info.get("passengerName"),
            "airlineInfo": credits_info.get("airlineInfo"),
            "totalFare": credits_info.get("totalFare"),
            "issueDate": credits_info.get("issueDate"),
            "expiryDate": credits_info.get("expiryDate"),
            "source": credits_info.get("source"),
            "sourcePnr": credits_info.get("sourcePnr"),
            "ticketNumber": credits_info.get("ticketNumber"),
            "flightIds": flight_ids,
        }
        # TODO: otherCoinage
        # payment_source["amount"] = {
        #     "amount": 0,
        #     "currencyCode": "USD",
        #     "convertedAmount": 0,
        #     "convertedCurrency": "USD",
        #     "otherCoinage": [
        #         {
        #             "coinageCode": "FLIGHT_CREDITS",
        #             "amount": credits_info.get("totalFare", 0),
        #             "conversionRate": 1,
        #             "preferredCurrencyConversionRate": 1,
        #         }
        #     ],
        # }

    @staticmethod
    async def get_spotnana_traveler_search_and_read(email: str):
        traveler_search = await FlightSearchTools.traveler_search_spotnana(email)
        traveler_read = await FlightSearchTools.traveler_read_spotnana(traveler_search["results"][0]["id"])

        return traveler_search, traveler_read

    @staticmethod
    async def check_user_payment_profile(traveler_search: dict[str, Any], user: User, admin_user: User | UserDB | None):
        is_updated: bool = False
        display_payment_profile_form: bool = False

        get_user_profile_tasks = [
            get_user_profile_personal_information(user.id),
            get_user_profile_payment_information(user.id),
        ]
        if admin_user:
            get_user_profile_tasks.append(get_user_profile_payment_information(admin_user.id))

        (user_profile_personal_information, user_profile_payment_information, *rest) = await asyncio.gather(
            *get_user_profile_tasks
        )
        admin_payment_information = rest[0] if admin_user else None

        is_user_personal_profile_complete: bool = is_user_profile_personal_information_complete(
            user_profile_personal_information
        )
        is_user_payment_profile_complete: bool = is_user_profile_payment_information_complete(
            user_profile_payment_information
        )
        is_admin_user_payment_profile_complete: bool = is_user_profile_payment_information_complete(
            admin_payment_information
        )

        if len(traveler_search["results"]) == 0:
            is_profile_complete: bool = is_user_personal_profile_complete and (
                is_user_payment_profile_complete or is_admin_user_payment_profile_complete
            )
            if not is_profile_complete:
                display_payment_profile_form = True

            elif is_user_personal_profile_complete:
                assert user_profile_personal_information is not None
                # Create the spotnana profile with the data we already have
                await save_personal_information_spotnana(
                    PersonalInformationExtendedRequest(**user_profile_personal_information), user, None
                )
                is_updated = True

            elif is_user_payment_profile_complete:
                assert user_profile_payment_information is not None
                await save_payment_information_spotnana(
                    PaymentInformationRequest(**user_profile_payment_information), user
                )
                is_updated = True

        return is_updated, display_payment_profile_form

    @staticmethod
    def trim_user_name_fields(user: dict[str, Any]) -> None:
        if user.get("personalInfo", {}).get("name") is not None:
            user["personalInfo"]["name"]["family1"] = (user["personalInfo"]["name"].get("family1") or "").strip()
            user["personalInfo"]["name"]["family2"] = (user["personalInfo"]["name"].get("family2") or "").strip()
            user["personalInfo"]["name"]["given"] = (user["personalInfo"]["name"].get("given") or "").strip()
            user["personalInfo"]["name"]["middle"] = (user["personalInfo"]["name"].get("middle") or "").strip()
            user["personalInfo"]["name"]["preferred"] = (user["personalInfo"]["name"].get("preferred") or "").strip()

    @staticmethod
    async def get_seat_map_with_loyalty_comparison(
        *,
        spotnana_search_id: str,
        spotnana_itinerary_id: str,
        loyaltyInfos: list | None,
        user_guid: str | None,
    ) -> tuple[dict, dict]:
        """
        Get seat maps with and without loyalty programs in parallel to identify loyalty-upgraded seats.

        Returns:
            tuple: (seat_map_with_loyalty, loyalty_upgraded_seats_info)
                - seat_map_with_loyalty: The seat map response with loyalty programs applied
                - loyalty_upgraded_seats_info: Dict mapping flight_seat_map_id to list of seats that became free due to loyalty
        """
        seat_map_with_loyalty_task = FlightSearchTools.seat_map_spotnana(
            searchId=spotnana_search_id,
            itineraryId=spotnana_itinerary_id,
            loyaltyInfos=loyaltyInfos,
            user_guid=user_guid,
        )

        seat_map_without_loyalty_task = FlightSearchTools.seat_map_spotnana(
            searchId=spotnana_search_id,
            itineraryId=spotnana_itinerary_id,
            loyaltyInfos=[],
            user_guid=user_guid,
        )

        seat_map_with_loyalty, seat_map_without_loyalty = await asyncio.gather(
            seat_map_with_loyalty_task, seat_map_without_loyalty_task
        )

        loyalty_upgraded_seats_info = {}

        seat_maps_with_loyalty = seat_map_with_loyalty.get("seatMaps", [])
        seat_maps_without_loyalty = seat_map_without_loyalty.get("seatMaps", [])

        for seat_map_with, seat_map_without in zip(seat_maps_with_loyalty, seat_maps_without_loyalty):
            seat_map_id = seat_map_with.get("seatMapId")

            _, prices_with_loyalty = FlightSearchTools.convert_seat_map_to_csv(seat_map_with)
            _, prices_without_loyalty = FlightSearchTools.convert_seat_map_to_csv(seat_map_without)

            loyalty_upgraded_seats = []
            for seat_id in prices_with_loyalty:
                price_without_loyalty = prices_without_loyalty.get(seat_id, 0)
                price_with_loyalty = prices_with_loyalty.get(seat_id, 0)

                if price_without_loyalty > 0 and price_with_loyalty == 0:
                    loyalty_upgraded_seats.append(
                        {
                            "seat_id": seat_id,
                            "original_price": price_without_loyalty,
                            "loyalty_price": price_with_loyalty,
                        }
                    )

            if loyalty_upgraded_seats:
                loyalty_upgraded_seats_info[seat_map_id] = loyalty_upgraded_seats

        return seat_map_with_loyalty, loyalty_upgraded_seats_info
