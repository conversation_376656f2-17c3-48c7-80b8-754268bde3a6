import asyncio
import json
from datetime import datetime, timezone
from typing import <PERSON><PERSON>, cast

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Request, Response
from google_auth_oauthlib.flow import Flow
from sqlalchemy import Row
from starlette.responses import JSONResponse

from server.database.models.chat_thread import ChatThread
from server.database.models.user_profile import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserProfile
from server.schemas.authenticate.google import (
    CalendarConnectRequest,
    CalendarConnectResponse,
    GoogleAuthenticateMobileModel,
    GoogleAuthenticateModel,
)
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.authenticate.google import GoogleAuthenticate
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.calendar_api.google_api_base import GoogleApiBase
from server.services.calendar_api.google_calendar_events import GoogleCalendarEvents
from server.services.feature_flags.feature_flag import FeatureFlags, is_feature_flag_enabled
from server.services.notifications.push_app_notifications import (
    send_event_notification,
)
from server.services.user.user_activity import update_user_activity
from server.services.user.user_session_history import get_user_last_session
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.google_login import (
    CALENDAR_SCOPES,
    GOOGLE_CLIENT_CONFIG,
    get_google_auth_url,
)
from server.utils.logger import logger
from server.utils.mongo_connector import google_calendar_events_collection
from server.utils.settings import settings

router = APIRouter()
lock = asyncio.Lock()


@router.post("/login/mobile-calendar-token")
async def get_mobile_calendar_token(
    request: Request, auth_code: GoogleAuthenticateMobileModel, user: User = Depends(manager)
):
    try:
        refresh_token = auth_code.refreshToken
        user_profile = await UserProfile.from_user_id(user.id)
        if user_profile is not None:
            await user_profile.enable_calendar(True)

        if auth_code.code:
            google = GoogleAuthenticate(code=auth_code.code, redirect_uri=f"{settings.CLIENT_DOMAIN}login")
            token = await google.fetch_token()

            refresh_token = cast(str, token.get("refresh_token"))

            if user_profile is None:
                google_auth = GoogleAuthenticate(
                    id_token=getattr(token, "id_token", None),
                    refresh_token=refresh_token,
                )
                _, user_id, _ = await google_auth.authorize(user_id_otp_override=user.id, request=request)

                user_profile = await UserProfile.from_user_id(user_id)

            # Check if user_profile exists before calling update_refresh_token
            if user_profile is not None:
                await user_profile.update_refresh_token(refresh_token, LoginMethod.GOOGLE)

        elif user_profile is None or user_profile.last_login_method != LoginMethod.GOOGLE:
            # Ensure refresh_token is not None before creating UserProfile
            if refresh_token is None:
                raise ValueError("Refresh token is required but not provided")

            google_refresh_token = GoogleApiBase(refresh_token)
            google_refresh_token.authorize()
            sub = google_refresh_token.get_sub()

            if sub is None:
                raise ValueError("Refresh token is not valid")

            google_user_profile = await UserProfile.from_sub(cast(str, sub)) if sub else None
            if google_user_profile is not None:
                await google_user_profile.update_refresh_token(refresh_token, LoginMethod.GOOGLE)
                user_profile = google_user_profile

            else:
                # Handle case when the user profile exists but with a different sub
                if user_profile is not None:
                    await user_profile.delete()

                user_profile = UserProfile(
                    sub=sub,
                    users_id=user.id,
                    refresh_token=refresh_token,  # Now guaranteed to be str, not None
                    resource_id=None,
                    last_login_method=LoginMethod.GOOGLE,
                )
                await UserProfile.new_user_profile(user_profile)

        assert refresh_token is not None, "Refresh token should be present"
        assert user_profile is not None, "User profile should be present"
        await user_profile.enable_calendar(True)

        await update_user_activity(
            user_id=str(user.id),
            activity_type="calendar_update",
            data={"calendar_connected": True, "calendar_type": "google"},
        )

        await TrackingManager.log_event_in_background(
            event_type=TrackingEvent.CALENDAR_ACCESS_GRANTED,
            user_id=str(user.id),
            user_email=user.email,
            extra="Google",
        )

        return JSONResponse({"status": "OK"})
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/login/calendar-token")
async def get_calendar_token(request: Request, auth_code: GoogleAuthenticateModel, user: User = Depends(manager)):
    try:
        # Initialize flow with only the calendar events scope
        flow = Flow.from_client_config(client_config=GOOGLE_CLIENT_CONFIG, scopes=CALENDAR_SCOPES)

        # Set the redirect URI after flow creation
        flow.redirect_uri = auth_code.redirect_uri

        # Exchange the authorization code for credentials
        flow.fetch_token(authorization_response=auth_code.redirect_uri, code=auth_code.code, include_client_id=True)

        credentials = flow.credentials

        user_profile = await UserProfile.from_user_id(user.id)
        if user_profile is not None:
            await user_profile.enable_calendar(True)

        assert credentials.refresh_token is not None, "Refresh token should be present"
        # assert getattr(credentials, "id_token", None) is not None, "Id token should be present"

        if user_profile is None:
            google_auth = GoogleAuthenticate(
                id_token=getattr(credentials, "id_token", None),
                refresh_token=credentials.refresh_token,
            )
            _, user_id, _ = await google_auth.authorize(user_id_otp_override=user.id, request=request)

            user_profile = await UserProfile.from_user_id(user_id)

        assert user_profile is not None, "User profile should be present"
        await user_profile.update_refresh_token(credentials.refresh_token, LoginMethod.GOOGLE)
        await user_profile.enable_calendar(True)

        await update_user_activity(
            user_id=str(user.id),
            activity_type="calendar_update",
            data={"calendar_connected": True, "calendar_type": "google"},
        )

        await TrackingManager.log_event_in_background(
            event_type=TrackingEvent.CALENDAR_ACCESS_GRANTED,
            user_id=str(user.id),
            user_email=user.email,
            extra="Google",
        )

        return JSONResponse({"status": "OK"})
    except Exception as e:
        logger.error(f"Error getting calendar token: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/login", response_class=JSONResponse)
async def login(request: Request, data: GoogleAuthenticateModel):
    otc_code: str | None = request.cookies.get("otc")

    if data.code:
        google = GoogleAuthenticate(code=data.code, redirect_uri=data.redirect_uri, otc_code=otc_code)

    elif data.token_id and data.refresh_token:
        google = GoogleAuthenticate(
            id_token=data.token_id,
            redirect_uri=data.redirect_uri,
            refresh_token=data.refresh_token,
            otc_code=otc_code,
        )
    else:
        raise HTTPException(status_code=400, detail="Authorization code or token ID is required.")

    (access_token, refresh_token), user_id, user_email = await google.authorize(request=request)

    await update_user_activity(user_id=str(user_id), activity_type="sign_in")

    session_history: Row[Tuple[ChatThread]] | None = await get_user_last_session(user_id)

    response = Response(
        content=json.dumps(
            {
                "accessToken": access_token,
                "refreshToken": refresh_token,
                "user_id": user_id,
                "user_email": user_email,
                "is_onboarding_completed": session_history is not None,
                "last_checkpoint_trip_id": (session_history.ChatThread.id if session_history is not None else None),
            }
        ),
        media_type="application/json",
    )

    response.set_cookie(
        key="access_token",
        value=access_token,
        samesite="none",
        secure=True,
        httponly=False,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.ACCESS_TOKEN_LIFESPAN,
    )
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        samesite="none",
        secure=True,
        httponly=True,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.REFRESH_TOKEN_LIFESPAN,
    )

    if otc_code is not None:
        response.delete_cookie(
            "otc",
            secure=True,
            domain=settings.COOKIES_DOMAIN,
        )

    return response


@router.post("/logout")
async def logout(user: User = Depends(manager)):
    response = Response(content=json.dumps({"status": "success"}), media_type="application/json")
    response.delete_cookie(
        "access_token",
        secure=(settings.COOKIES_DOMAIN != "localhost"),
        domain=settings.COOKIES_DOMAIN,
    )
    response.delete_cookie(
        "refresh_token",
        secure=(settings.COOKIES_DOMAIN != "localhost"),
        domain=settings.COOKIES_DOMAIN,
    )

    return response


@router.post("/calendar-connect", response_model=CalendarConnectResponse)
async def google_calendar_connect(request: CalendarConnectRequest, user: User = Depends(manager)):
    """
    Connect or disconnect Google Calendar integration
    """
    try:
        user_profile = await UserProfile.from_user_id(user.id)

        if request.enable:
            return await _handle_enable_google_calendar(user_profile, user.email)
        else:
            return await _handle_disable_google_calendar(user_profile)

    except Exception as e:
        logger.error(f"Microsoft calendar connection error for user {user.id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process Microsoft calendar connection")


async def _handle_enable_google_calendar(user_profile: UserProfile | None, email: str) -> CalendarConnectResponse:
    def _get_auth_url() -> str:
        return get_google_auth_url(email, redirect_uri="profile/preferences")

    if user_profile is None:
        return CalendarConnectResponse(enabled=False, calendarAuthUrl=_get_auth_url())

    if user_profile.last_login_method == LoginMethod.MICROSOFT:
        return CalendarConnectResponse(enabled=False, calendarAuthUrl=_get_auth_url())

    if user_profile.calendar_enabled:
        return CalendarConnectResponse(enabled=True)

    calendar_provider = CalendarProviderManager(user_profile=user_profile, user_email=email)
    has_calendar_access = calendar_provider.has_calendar_access()
    if not has_calendar_access:
        return CalendarConnectResponse(enabled=False, calendarAuthUrl=_get_auth_url())

    await user_profile.enable_calendar(True)
    return CalendarConnectResponse(enabled=True)


async def _handle_disable_google_calendar(user_profile: UserProfile | None) -> CalendarConnectResponse:
    if user_profile is not None:
        await user_profile.enable_calendar(False)

    return CalendarConnectResponse(enabled=False)


@router.post("/calendar-webhook")
async def calendar_webhook(request: Request):
    headers = request.headers
    shared_secret = request.query_params.get("shared_secret")
    if not shared_secret:
        raise HTTPException(status_code=403, detail="Forbidden")

    user_email = request.query_params.get("user_email")
    if not user_email:
        raise HTTPException(status_code=400, detail="User ID not provided")

    resource_state = headers.get("X-Goog-Resource-State")
    logger.debug(f"X-Goog-Resource-State={resource_state}")

    channel_id = headers.get("X-Goog-Channel-ID")
    resource_id = headers.get("X-Goog-Resource-ID")

    if resource_state == "exists":
        google_calendar = GoogleCalendarEvents(shared_secret)

        # Fetch the last sync token from the database
        user_events_doc = await google_calendar_events_collection.find_one({"user_id": user_email})
        sync_token = user_events_doc.get("sync_token") if user_events_doc else None

        # Fetch events from Google Calendar using sync token
        events_result, next_sync_token = google_calendar.get_events_by_sync_token(sync_token=sync_token)
        if not events_result and not next_sync_token:
            google_calendar.stop_watch(channel_id, resource_id)
            return JSONResponse({"status": "OK"})

        new_events = google_calendar.extract_relevant_fields(events_result)

        existing_events = user_events_doc.get("events", []) if user_events_doc else []
        existing_event_ids = {event["id"] for event in existing_events if event.get("id") is not None}

        new_events_filtered = [
            {**event, "is_new": True} for event in new_events if event["id"] not in existing_event_ids
        ]

        if new_events_filtered:
            await asyncio.gather(*[process_event(user_email, event) for event in new_events_filtered])

            # Update sync token
            if next_sync_token:
                await google_calendar_events_collection.update_one(
                    {"user_id": user_email},
                    {"$set": {"sync_token": next_sync_token}},
                    upsert=True,
                )

    return JSONResponse({"status": "OK"})


async def process_event(user_email, new_event):
    try:
        res = await google_calendar_events_collection.update_one(
            {"user_id": user_email}, {"$addToSet": {"events": new_event}}, upsert=True
        )
        assert res.raw_result is not None, "Expecting raw_result to be returned after update_one() call"
        should_send_notification = res.raw_result.get("upserted") is not None or res.raw_result.get("nModified") == 1
        if should_send_notification:
            # Check if future trip notifications are enabled for this user
            notifications_enabled = await is_feature_flag_enabled(
                user_email, FeatureFlags.ENABLE_FUTURE_TRIP_NOTIFICATIONS
            )
            if not notifications_enabled:
                logger.info(
                    f"Push notifications for future trips are not enabled by feature flag for user {user_email}"
                )
                return
            await send_event_notification(new_event, user_email)

    except Exception as e:
        logger.error(f"Failed to update events in DB: {e}")
        raise
