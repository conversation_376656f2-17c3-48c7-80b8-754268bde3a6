import asyncio
from datetime import datetime, timezone
from typing import Any, Optional, cast
from urllib.parse import urljoin

from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi.responses import PlainTextResponse
from pymongo.collation import Collation
from starlette.responses import JSONResponse, RedirectResponse

from server.api.v1.endpoints.authenticate.google import process_event
from server.database.models.chat_thread import ChatThread
from server.database.models.user import User
from server.database.models.user_profile import Lo<PERSON><PERSON>ethod, UserProfile
from server.schemas.authenticate.google import CalendarConnectRequest, CalendarConnectResponse
from server.services.authenticate.authenticate import manager, validate_user
from server.services.authenticate.microsoft import exchange_code_for_token, get_microsoft_auth_url, persist_state
from server.services.calendar_api.calendar_provider import CalendarProviderManager
from server.services.calendar_api.microsoft_calendar_events import MicrosoftCalendarEvents
from server.services.microsoft_graph_api.get_profile_picture import get_profile_picture
from server.services.one_time_codes.manage import create_otc_email_cookie, redeem_otc
from server.services.organization.organization import OrganizationService
from server.services.user.user_activity import update_user_activity
from server.utils.analytics.analytics import TrackingEvent, TrackingManager
from server.utils.ip_stack_api import get_client_ip, guess_and_update_home_airport
from server.utils.logger import logger
from server.utils.mongo_connector import google_calendar_events_collection, users_whitelist_collection
from server.utils.sentry import beta_login_to_sentry, not_invited_login_to_sentry
from server.utils.settings import settings

router = APIRouter()


@router.get("/login")
async def login():
    """
    Initiate Microsoft OAuth login
    """

    auth_url = get_microsoft_auth_url()
    return JSONResponse({"url": auth_url})


async def validate_subscription(request: Request):
    """
    Validates incoming subscription requests and processes calendar events
    """
    headers = request.headers
    logger.info(f"Received webhook request with headers: {headers}")

    # Handle initial subscription validation
    validation_token = request.query_params.get("validationToken")
    if validation_token:
        return PlainTextResponse(content=validation_token)

    shared_secret = request.query_params.get("shared_secret")
    if not shared_secret:
        raise HTTPException(status_code=403, detail="Forbidden")

    user_email = request.query_params.get("user_email")
    if not user_email:
        raise HTTPException(status_code=403, detail="Forbidden")

    microsoft_calendar = MicrosoftCalendarEvents(refresh_token=shared_secret)
    try:
        body = await request.json()
        notifications = body.get("value", [])
        processed_events = []

        for notification in notifications:
            resource = notification.get("resource", "")
            resource_id = notification.get("id", "")

            change_type = notification.get("changeType", "")

            logger.info(f"Processing {change_type} event from resource: {resource}")

            # Only process created or updated events
            if change_type in ["created", "updated"]:
                user_events_doc = await google_calendar_events_collection.find_one({"user_id": user_email})

                existing_events = user_events_doc.get("events", []) if user_events_doc else []
                existing_event_ids = {event["id"] for event in existing_events if event.get("id") is not None}

                # event_details = await extract_event_details(resource)
                event_details = microsoft_calendar.get_event_by_id(resource_id)
                events = microsoft_calendar.extract_relevant_fields(event_details)

                new_events_filtered = [
                    {**event, "is_new": True} for event in events if event["id"] not in existing_event_ids
                ]

                if new_events_filtered:
                    await asyncio.gather(*[process_event(user_email, event) for event in new_events_filtered])

        return {
            "status": "success",
            "message": f"Processed {len(notifications)} notifications",
            "processed_events": processed_events,
        }

    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error processing webhook: {str(e)}")


@router.post("/calendar-webhook")
async def calendar_webhook(request: Request):
    return await validate_subscription(request)


@router.post("/calendar-connect", response_model=CalendarConnectResponse)
async def microsoft_calendar_connect(request: CalendarConnectRequest, user: User = Depends(manager)):
    """
    Connect or disconnect Microsoft Calendar integration
    """
    try:
        user_profile = await UserProfile.from_user_id(user.id)

        if request.enable:
            return await _handle_enable_microsoft_calendar(user_profile, user.email)
        else:
            return await _handle_disable_microsoft_calendar(user_profile)

    except Exception as e:
        logger.error(f"Microsoft calendar connection error for user {user.id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process Microsoft calendar connection")


async def _handle_enable_microsoft_calendar(user_profile: UserProfile | None, email: str) -> CalendarConnectResponse:
    def _get_auth_url() -> str:
        return get_microsoft_auth_url(state="preferences", scopes=["User.Read", "Calendars.ReadWrite"])

    # No user profile - require authentication
    if user_profile is None:
        return CalendarConnectResponse(enabled=False, calendarAuthUrl=_get_auth_url())

    # User logged in with Google - require Microsoft auth regardless of calendar status
    if user_profile.last_login_method == LoginMethod.GOOGLE:
        return CalendarConnectResponse(enabled=False, calendarAuthUrl=_get_auth_url())

    # Calendar already enabled - return success
    if user_profile.calendar_enabled:
        return CalendarConnectResponse(enabled=True)

    calendar_provider = CalendarProviderManager(user_profile=user_profile, user_email=email)
    has_calendar_access = calendar_provider.has_calendar_access()
    if not has_calendar_access:
        return CalendarConnectResponse(enabled=False, calendarAuthUrl=_get_auth_url())

    # Enable calendar and return success
    await user_profile.enable_calendar(True)
    return CalendarConnectResponse(enabled=True)


async def _handle_disable_microsoft_calendar(user_profile: UserProfile | None) -> CalendarConnectResponse:
    if user_profile is not None:
        await user_profile.enable_calendar(False)

    return CalendarConnectResponse(enabled=False)


@router.get("/callback")
async def auth_callback(
    request: Request,
    code: Optional[str] = None,
    error: Optional[str] = None,
    error_description: Optional[str] = None,
    state: Optional[str] = None,
):
    """
    Handle Microsoft OAuth callback
    """
    if error:
        if state == "onboarding":
            assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"
            app_redirect_url = (
                urljoin(settings.CLIENT_DOMAIN, "/onboarding") + f"?calendar_granted=false&reason={error_description}"
            )
        else:
            app_redirect_url = f"{settings.CLIENT_DOMAIN}?calendar_granted=false&reason={error_description}"
        return RedirectResponse(url=app_redirect_url)

    if not code:
        raise HTTPException(status_code=400, detail="Authorization code is required")

    try:
        # Exchange code for token and get user info
        token_response = await exchange_code_for_token(code)
        response = await handle_auth_with_token(request, code, token_response, state)

        return response
    except HTTPException as e:
        assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"
        app_redirect_url = settings.CLIENT_DOMAIN
        if state == "onboarding":
            app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/onboarding") + "?calendar_granted=false"
        response = RedirectResponse(url=app_redirect_url)
        raise e


async def handle_auth_with_token(
    request: Request,
    code: str,
    token_response: dict[str, Any],
    state: str | None = None,
    redirect_response: bool = True,
):
    """
    Handle Microsoft authentication with token.
    Ensures only one UserProfile per user by prioritizing user identification over Microsoft sub.
    """
    user_info: dict = cast(dict, token_response.get("user_info"))

    email_lower = user_info["email"].lower()
    whitelisted_email = await users_whitelist_collection.find_one(
        {"whitelisted_emails": email_lower}, collation=Collation(locale="en", strength=2)
    )

    otc_code: str | None = request.cookies.get("otc")
    if whitelisted_email is None and otc_code is not None:
        is_valid: bool = await redeem_otc(otc_code, email_lower)
        if is_valid:
            whitelisted_email = email_lower

    bypass_whitelist_restrictions = OrganizationService.can_bypass_whitelist_restrictions(email_lower)
    if whitelisted_email is None and not bypass_whitelist_restrictions:
        logger.info(f"{email_lower} - Redirect. Email is not whitelisted for this domain and no OTC provided.")
        not_invited_login_to_sentry(email_lower, "Microsoft")

        await persist_state(email_lower, {"code": code, "token_response": token_response, "state": state})
        return RedirectResponse(
            url=f"{settings.CLIENT_DOMAIN}/login-denied", headers={"set-cookie": create_otc_email_cookie(email_lower)}
        )
    else:
        beta_login_to_sentry(user_info["email"], "Microsoft")

    # Prioritize user identification by email over Microsoft sub
    user_profile: UserProfile | None = None
    user: User | None = None
    refresh_token: str | None = token_response.get("refresh_token")
    microsoft_sub: str = user_info.get("sub", "")

    assert refresh_token is not None, "just logged in, should have refresh_token"

    # 1. First try to find existing user by email
    user = await User.from_email(user_info["email"])

    if user is not None:
        # User exists - get their profile by user_id (not by sub)
        user_profile = await UserProfile.from_user_id(user.id)

        if user_profile is not None:
            # Update existing profile with new Microsoft sub and refresh token
            await user_profile.update_refresh_token(refresh_token, LoginMethod.MICROSOFT)
        else:
            # User exists but no profile - create one
            user_profile = UserProfile(
                sub=microsoft_sub,
                users_id=user.id,
                refresh_token=refresh_token,
                resource_id=None,
                last_login_method=LoginMethod.MICROSOFT,
            )
            await UserProfile.new_user_profile(user_profile)
    else:
        # 2. If no user found by email, try to find by Microsoft sub (for backward compatibility)
        user_profile = await UserProfile.from_sub(microsoft_sub) if microsoft_sub else None

        if user_profile is not None:
            # Profile exists with this sub - get the associated user
            user = await validate_user(user_profile.users_id)
            await user_profile.update_refresh_token(refresh_token, LoginMethod.MICROSOFT)
        else:
            # 3. Completely new user - create both user and profile
            # Get default profile picture for the first time
            profile_picture_url = await get_profile_picture(token_response.get("access_token"))
            user = User(
                user_info.get("email", ""),
                user_info.get("given_name", "").strip(),
                user_info.get("family_name", "").strip(),
                profile_picture=profile_picture_url or f"{settings.SERVER_DNS}/static/default-avatar-icon.jpg",
            )

            user_profile = await UserProfile.new_user(
                microsoft_sub, refresh_token, None, LoginMethod.MICROSOFT, user=user
            )

            # First time user login, let's guess their home airport
            client_ip = get_client_ip(request)
            asyncio.create_task(guess_and_update_home_airport(client_ip, user.id))

            await TrackingManager.log_event_in_background(
                event_type=TrackingEvent.FIRST_LOGIN,
                user_id=str(user.id),
                user_email=user.email,
                event_properties={"login_method": "Microsoft"},
            )

            await ChatThread.create_initial_chat_threads(user.id)

    # Update user fields with fresh Microsoft data if this is an existing user
    if user is not None and user_profile is not None and user_profile.last_login_method == LoginMethod.MICROSOFT:
        profile_picture_url = await get_profile_picture(token_response.get("access_token"))

        await user.refresh_fields(
            {
                "first_name": user_info["given_name"],
                "last_name": user_info["family_name"],
                "profile_picture": profile_picture_url or f"{settings.SERVER_DNS}/static/default-avatar-icon.jpg",
            }
        )

    assert user_profile is not None, "user_profile should not be None at this point"
    assert user is not None, "User should not be None at this point"

    is_organisation_assigned, message = await OrganizationService.auto_assign_user_to_organization(user)
    if not is_organisation_assigned:
        # TODO Needs to implement an warnings in case of errors.
        pass

    # Handle Microsoft Calendar integration
    microsoft_calendar = MicrosoftCalendarEvents(refresh_token, user_email=user.email)

    if microsoft_calendar.has_calendar_access():
        await TrackingManager.log_event_in_background(
            event_type=TrackingEvent.CALENDAR_ACCESS_GRANTED, user_id=str(user.id), user_email=user.email
        )
        resource_id = microsoft_calendar.watch_calendar()
        if resource_id:
            await user_profile.update_resource_id(resource_id)

        await user_profile.enable_calendar(True)

    elif state == "onboarding":
        assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"
        app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/onboarding") + "?calendar_granted=false"
        return RedirectResponse(url=app_redirect_url)

    payload: dict[str, str] = user_profile.get_token_payload(user=user)

    access_token, refresh_token = manager.create_custom_jwt(payload)

    assert settings.CLIENT_DOMAIN is not None, "CLIENT_DOMAIN is not set"
    app_redirect_url = settings.CLIENT_DOMAIN
    if state == "onboarding":
        app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/onboarding") + "?calendar_granted=true"

    if state == "preferences":
        app_redirect_url = urljoin(settings.CLIENT_DOMAIN, "/profile/preferences") + "?calendar_granted=true"

    await update_user_activity(user_id=str(user.id), activity_type="sign_in")

    response = RedirectResponse(url=app_redirect_url) if redirect_response else Response()
    response.set_cookie(
        key="access_token",
        value=access_token,
        samesite="none",
        secure=True,
        httponly=False,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.ACCESS_TOKEN_LIFESPAN,
    )
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        samesite="none",
        secure=True,
        httponly=True,
        domain=settings.COOKIES_DOMAIN,
        expires=datetime.now(tz=timezone.utc) + settings.REFRESH_TOKEN_LIFESPAN,
    )

    if otc_code is not None:
        response.delete_cookie(
            "otc",
            secure=True,
            domain=settings.COOKIES_DOMAIN,
        )

    return response
