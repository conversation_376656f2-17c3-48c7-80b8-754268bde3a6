from datetime import datetime, timedelta, timezone
from typing import List, Sequence, Tuple
from zoneinfo import ZoneInfo

import dateutil.parser
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, Head<PERSON>
from sqlalchemy import Row, func, select
from starlette.responses import JSONResponse

from hotel_agent.booking_dot_com_models import BookingStatus
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.database.models.checkpoint import Checkpoint
from server.schemas.authenticate.user import User
from server.services.authenticate.authenticate import manager
from server.services.trips.bookings import add_booking_action_strings, format_bookings
from server.utils.logger import logger
from server.utils.pg_connector import async_session
from server.utils.settings import settings

router = APIRouter()


@router.get("/trips/list")
async def list_trips(
    user: User = Depends(manager),
    x_timezone: str | None = Header(default=None),
) -> JSONResponse:
    trips = await ChatThread.from_user_id(user.id, filter_only_upcoming=False)
    chat_thread_ids = [trip.ChatThread.id for trip in trips]

    # Get bookings map
    thread_bookings_map = await get_trip_bookings_map(chat_thread_ids)

    # Get last timestamps
    async with async_session() as session:
        async with session.begin():
            trips_last_timestamps = await get_last_timestamps(chat_thread_ids, session)

    # Retrieve timezone information if provided
    try:
        tz = ZoneInfo(x_timezone) if x_timezone else None
    except Exception:
        tz = None

    # Categorize and sort trips
    (
        planned_trips,
        booked_trips,
        past_trips,
        saved_trips,
        preferences_thread_id,
        travel_policy_thread_id,
        onboarding_thread_id,
    ) = categorize_and_sort_trips(trips, thread_bookings_map, trips_last_timestamps, tz=tz)

    # Format for response
    planned, booked, past, saved = format_trips_for_response(
        planned_trips, booked_trips, past_trips, saved_trips, thread_bookings_map
    )

    return JSONResponse(
        content={
            "planned": planned,
            "booked": booked,
            "past": past,
            "saved": saved,
            "preferences_thread_id": preferences_thread_id,
            "travel_policy_thread_id": travel_policy_thread_id,
            "onboarding_thread_id": onboarding_thread_id,
        }
    )


async def get_trip_bookings_map(chat_thread_ids: list[int]) -> dict:
    # Fetch all relevant chat threads from MongoDB in a single query
    booked_threads: list[Booking] = await Booking.from_query_batch({"thread_id": chat_thread_ids})

    # Create a dictionary to map thread_id to its bookings
    thread_bookings_map = {}
    for booking in booked_threads:
        thread_id = booking.thread_id
        if thread_id not in thread_bookings_map:
            thread_bookings_map[thread_id] = []
        thread_bookings_map[thread_id].append(booking)

    return thread_bookings_map


async def get_last_timestamps(chat_thread_ids: list[int], session) -> dict:
    """Get last checkpoint timestamps for chat threads"""
    trips_last_timestamps = {}
    if chat_thread_ids:
        # Build SQLAlchemy query
        query = (
            select(ChatThread.id, func.max(Checkpoint.created_date).label("last_timestamp"))
            .outerjoin(Checkpoint, ChatThread.id == Checkpoint.thread_id)
            .where(ChatThread.id.in_(chat_thread_ids))
            .group_by(ChatThread.id)
        )

        # Execute the query
        result = await session.execute(query)

        # Process results
        for row in result:
            if row.last_timestamp:
                trips_last_timestamps[row.id] = row.last_timestamp.isoformat()
            else:
                trips_last_timestamps[row.id] = None

    return trips_last_timestamps


def categorize_and_sort_trips(
    trips: Sequence[Row[Tuple[ChatThread]]],
    thread_bookings_map: dict,
    trips_last_timestamps: dict,
    tz: ZoneInfo | timezone | None = None,
):
    # Create separate lists for each category using the ORM objects
    planned_trips: List = []
    booked_trips: List = []
    past_trips: List = []
    saved_trips: List = []

    preferences_thread_id: int | None = None
    travel_policy_thread_id: int | None = None
    onboarding_thread_id: int | None = None

    now = datetime.now(tz=tz or timezone.utc)
    datetime_threshold = now.replace(hour=0, minute=0, second=0, microsecond=0)
    if not tz:
        datetime_threshold = datetime_threshold - timedelta(hours=12)
        logger.warning(
            "No timezone provided from client. Use UTC minus 12 hours. This may lead to incorrect trip categorization."
        )

    special_thread_titles = (
        settings.ONBOARDING_THREAD_TITLE,
        settings.PREFERENCES_THREAD_TITLE,
        settings.FUTURE_TRIPS_THREAD_TITLE,
        settings.TRAVEL_POLICY_THREAD_TITLE,
        settings.ONBOARDING_PAGE_TITLE,
    )

    for trip in trips:
        thread = trip.ChatThread

        # Handle special threads
        if thread.title == settings.PREFERENCES_THREAD_TITLE:
            preferences_thread_id = thread.id
            continue
        elif thread.title == settings.TRAVEL_POLICY_THREAD_TITLE:
            travel_policy_thread_id = thread.id
            continue
        elif thread.title == settings.ONBOARDING_PAGE_TITLE:
            onboarding_thread_id = thread.id
            continue
        elif thread.title in special_thread_titles:
            continue

        bookings = thread_bookings_map.get(thread.id, [])

        if bookings:
            is_upcoming = is_trip_upcoming(bookings, thread, datetime_threshold)
        else:
            # For planned trips (no bookings), use thread dates
            is_upcoming = (
                (thread.date_end is not None and thread.date_end.replace(tzinfo=timezone.utc) >= now)
                or (
                    thread.date_end is None
                    and thread.date_start is not None
                    and thread.date_start.replace(tzinfo=timezone.utc) >= now
                )
                or (thread.date_end is None and thread.date_start is None)
            )

        # Categorize trip
        if not is_upcoming:
            past_trips.append(trip)
        elif thread.id in thread_bookings_map:
            booked_trips.append(trip)
        elif isinstance(thread.extra, dict) and thread.extra.get("savedFromTrip"):
            saved_trips.append(trip)
        else:
            planned_trips.append(trip)

    # Sort planned trips by last checkpoint timestamp (most recent first)
    def planned_sort_key(trip):
        thread_id = trip.ChatThread.id
        last_timestamp = trips_last_timestamps.get(thread_id)
        if last_timestamp:
            return last_timestamp
        else:
            # Fallback to created_date if no checkpoint
            return trip.ChatThread.created_date.isoformat()

    planned_trips.sort(key=planned_sort_key, reverse=True)

    # Sort booked trips - most recently coming trip first (based on date_start)
    def booked_sort_key(trip):
        # Handle None date_start values by placing them at the end
        if trip.ChatThread.date_start is None:
            # Use the maximum date possible as a fallback
            return datetime.max.replace(tzinfo=timezone.utc)
        return trip.ChatThread.date_start.replace(tzinfo=timezone.utc)

    booked_trips.sort(key=booked_sort_key)

    # Sort past trips - most recent past trip first (based on date_end or date_start)
    def past_sort_key(trip):
        thread = trip.ChatThread
        if thread.date_end is not None:
            return thread.date_end.replace(tzinfo=timezone.utc)
        elif thread.date_start is not None:
            return thread.date_start.replace(tzinfo=timezone.utc)
        # Use the minimum date possible as a fallback for trips without dates
        return datetime.min.replace(tzinfo=timezone.utc)

    past_trips.sort(key=past_sort_key, reverse=True)

    return (
        planned_trips,
        booked_trips,
        past_trips,
        saved_trips,
        preferences_thread_id,
        travel_policy_thread_id,
        onboarding_thread_id,
    )


def format_trips_for_response(planned_trips, booked_trips, past_trips, saved_trips, thread_bookings_map):
    """Convert sorted trip objects to dictionaries for the response"""
    planned = []
    booked = []
    past = []
    saved = []

    # Process saved trips
    for trip in saved_trips:
        thread = trip.ChatThread
        trip_data = {
            "id": thread.id,
            "title": thread.title,
            "created_date": thread.created_date.replace(tzinfo=timezone.utc).isoformat(),
            "date_start": (
                thread.date_start.replace(tzinfo=timezone.utc).isoformat() if thread.date_start is not None else None
            ),
            "date_end": (
                thread.date_end.replace(tzinfo=timezone.utc).isoformat() if thread.date_end is not None else None
            ),
        }
        saved.append(trip_data)

    # Process planned trips
    for trip in planned_trips:
        thread = trip.ChatThread
        trip_data = {
            "id": thread.id,
            "title": thread.title,
            "created_date": thread.created_date.replace(tzinfo=timezone.utc).isoformat(),
            "date_start": (
                thread.date_start.replace(tzinfo=timezone.utc).isoformat() if thread.date_start is not None else None
            ),
            "date_end": (
                thread.date_end.replace(tzinfo=timezone.utc).isoformat() if thread.date_end is not None else None
            ),
            "extra": {**(thread.extra or {}), "isDeleted": thread.is_deleted},
        }
        planned.append(trip_data)

    # Process booked trips
    for trip in booked_trips:
        thread = trip.ChatThread
        trip_data = {
            "id": thread.id,
            "title": thread.title,
            "created_date": thread.created_date.replace(tzinfo=timezone.utc).isoformat(),
            "date_start": (
                thread.date_start.replace(tzinfo=timezone.utc).isoformat() if thread.date_start is not None else None
            ),
            "date_end": (
                thread.date_end.replace(tzinfo=timezone.utc).isoformat() if thread.date_end is not None else None
            ),
        }

        # Add itinerary data
        itinerary = format_bookings(thread_bookings_map[thread.id], thread)
        trip_data["itinerary"] = itinerary
        add_booking_action_strings(trip_data)

        booked.append(trip_data)

    # Process past trips
    for trip in past_trips:
        thread = trip.ChatThread
        trip_data = {
            "id": thread.id,
            "title": thread.title,
            "created_date": thread.created_date.replace(tzinfo=timezone.utc).isoformat(),
            "date_start": (
                thread.date_start.replace(tzinfo=timezone.utc).isoformat() if thread.date_start is not None else None
            ),
            "date_end": (
                thread.date_end.replace(tzinfo=timezone.utc).isoformat() if thread.date_end is not None else None
            ),
        }

        # Add itinerary data if available
        if thread.id in thread_bookings_map:
            itinerary = format_bookings(thread_bookings_map[thread.id], thread)
            trip_data["itinerary"] = itinerary

        past.append(trip_data)

    return planned, booked, past, saved


def is_trip_upcoming(bookings: list[Booking], thread, datetime_threshold: datetime) -> bool:
    # Track component types and completion status
    has_flights = False
    has_accommodations = False
    all_flights_completed = True
    all_accommodations_completed = True

    for booking in bookings:
        if booking.type == "flight":
            has_flights = True
            content = booking.content

            arrival_time = None
            try:
                # Legs is a list of flight legs, each containing flight segments, which in turn contain flight stops
                if "legs" in content:
                    legs = content.get("legs", [])
                    last_segment = legs[-1].get("flight_segments") if legs else []
                    last_stops = last_segment[-1].get("flight_stops") if last_segment else []
                    arrival_time = last_stops[-1].get("arrival") if last_stops else None

                # If no legs (legacy booking), check for outbound and return fields
                if not arrival_time and "outbound" in content:
                    outbound_flight_segments = content.get("outbound", {}).get("flight_segments") or []
                    outbound_flight_stops = (
                        (outbound_flight_segments[0].get("flight_stops") or []) if outbound_flight_segments else []
                    )
                    return_flight_segments = (
                        (content.get("return") or {}).get("flight_segments", []) if "return" in content else []
                    )
                    return_flight_stops = (
                        (return_flight_segments[0].get("flight_stops") or []) if return_flight_segments else []
                    )
                    arrival_time = (
                        return_flight_stops[-1].get("arrival")
                        if return_flight_stops
                        else outbound_flight_stops[-1].get("arrival")
                        if outbound_flight_stops
                        else None
                    )

                if arrival_time is not None:
                    arrival_time = dateutil.parser.isoparse(arrival_time)

            except (KeyError, IndexError, ValueError, TypeError):
                # If there's any error extracting arrival time, consider it missing
                arrival_time = None

            if arrival_time is None or arrival_time.replace(tzinfo=timezone.utc) > datetime_threshold:
                all_flights_completed = False

        elif booking.type == "accommodations":
            has_accommodations = True
            content = booking.content

            if not content:
                all_accommodations_completed = False
                continue

            is_cancelled = content.get("status", None) == BookingStatus.CANCELLED.value

            try:
                if is_cancelled:
                    check_in_datetime = None
                    thread_date_start_str = thread.date_start.strftime("%Y-%m-%d") if thread.date_start else None

                    if "check_in_date" in content and "check_in_time" in content:
                        check_in_str = (
                            f"{content.get('check_in_date', None) or thread_date_start_str} {content['check_in_time']}"
                        )
                        check_in_datetime = dateutil.parser.isoparse(check_in_str)

                    if check_in_datetime is None or check_in_datetime.replace(tzinfo=timezone.utc) > datetime_threshold:
                        all_accommodations_completed = False
                else:
                    # For active accommodations, check the check-out date
                    check_out_datetime = None
                    thread_date_end_str = thread.date_end.strftime("%Y-%m-%d") if thread.date_end else None

                    if "check_out_time" in content and ("check_out_date" in content or thread_date_end_str):
                        check_out_str = (
                            f"{content.get('check_out_date', None) or thread_date_end_str} {content['check_out_time']}"
                        )
                        check_out_datetime = dateutil.parser.isoparse(check_out_str)

                    if (
                        check_out_datetime is None
                        or check_out_datetime.replace(tzinfo=timezone.utc) > datetime_threshold
                    ):
                        all_accommodations_completed = False
            except (ValueError, TypeError):
                all_accommodations_completed = False

    if has_flights and has_accommodations:
        return not (all_flights_completed and all_accommodations_completed)
    elif has_flights:
        return not all_flights_completed
    elif has_accommodations:
        return not all_accommodations_completed
    else:
        return (
            (thread.date_end is not None and thread.date_end.replace(tzinfo=timezone.utc) >= datetime_threshold)
            or (
                thread.date_end is None
                and thread.date_start is not None
                and thread.date_start.replace(tzinfo=timezone.utc) >= datetime_threshold
            )
            or (thread.date_end is None and thread.date_start is None)
        )
